#!/usr/bin/env node

/**
 * Authentication Implementation Validation Script
 * Validates that all authentication components are properly implemented
 */

const fs = require('fs');
const path = require('path');

const requiredFiles = [
  // Core configuration
  'src/config/ApiConfig.ts',
  'src/config/environment.ts',
  
  // Interfaces
  'src/interfaces/IAuthentication.ts',
  'src/types/global.d.ts',
  
  // Services
  'src/services/HttpClientService.ts',
  'src/services/AuthenticationService.ts',
  'src/services/TokenStorageService.ts',
  
  // Controllers
  'src/controllers/AuthenticationController.ts',
  
  // IPC Handlers
  'src/ipc/authenticationHandlers.ts',
  
  // Components
  'src/renderer/components/auth/AuthGuard.tsx',
  'src/renderer/components/auth/GuestGuard.tsx',
  'src/renderer/components/common/ErrorBoundary.tsx',
  'src/renderer/components/settings/EnvironmentSettings.tsx',
  
  // Redux
  'src/renderer/store/slices/authSlice.ts',
  
  // Utils
  'src/utils/errorHandler.ts',
  
  // Tests
  'src/__tests__/config/ApiConfig.test.ts',
  'src/__tests__/utils/errorHandler.test.ts',
  'src/__tests__/integration/authentication.integration.test.ts',
];

const requiredUpdatedFiles = [
  'src/main/preload.ts',
  'src/ipc/ipcHandlers.ts',
  'src/renderer/store/reducer.ts',
  'src/renderer/routes/AuthenticationRoutes.tsx',
  'src/renderer/routes/MainRoutes.tsx',
  'src/renderer/views/pages/authentication/auth-forms/AuthLogin.tsx',
  'src/renderer/views/pages/authentication/auth-forms/AuthRegister.tsx',
];

function checkFileExists(filePath) {
  const fullPath = path.join(process.cwd(), filePath);
  return fs.existsSync(fullPath);
}

function checkFileContent(filePath, requiredContent) {
  const fullPath = path.join(process.cwd(), filePath);
  if (!fs.existsSync(fullPath)) {
    return false;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  return requiredContent.every(item => content.includes(item));
}

function validateImplementation() {
  console.log('🔍 Validating Authentication Implementation...\n');
  
  let allValid = true;
  
  // Check required new files
  console.log('📁 Checking required new files:');
  for (const file of requiredFiles) {
    const exists = checkFileExists(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
    if (!exists) allValid = false;
  }
  
  console.log('\n📝 Checking updated files:');
  
  // Check preload.ts for authAPI
  const preloadValid = checkFileContent('src/main/preload.ts', [
    'authAPI',
    'auth:login',
    'auth:register',
    'auth:logout'
  ]);
  console.log(`  ${preloadValid ? '✅' : '❌'} src/main/preload.ts (authAPI exposed)`);
  if (!preloadValid) allValid = false;
  
  // Check ipcHandlers.ts for authentication handlers
  const ipcHandlersValid = checkFileContent('src/ipc/ipcHandlers.ts', [
    'registerAuthenticationHandlers'
  ]);
  console.log(`  ${ipcHandlersValid ? '✅' : '❌'} src/ipc/ipcHandlers.ts (auth handlers registered)`);
  if (!ipcHandlersValid) allValid = false;
  
  // Check reducer.ts for auth slice
  const reducerValid = checkFileContent('src/renderer/store/reducer.ts', [
    'authReducer',
    'auth:'
  ]);
  console.log(`  ${reducerValid ? '✅' : '❌'} src/renderer/store/reducer.ts (auth slice added)`);
  if (!reducerValid) allValid = false;
  
  // Check AuthenticationRoutes.tsx for GuestGuard
  const authRoutesValid = checkFileContent('src/renderer/routes/AuthenticationRoutes.tsx', [
    'GuestGuard'
  ]);
  console.log(`  ${authRoutesValid ? '✅' : '❌'} src/renderer/routes/AuthenticationRoutes.tsx (GuestGuard added)`);
  if (!authRoutesValid) allValid = false;
  
  // Check MainRoutes.tsx for AuthGuard
  const mainRoutesValid = checkFileContent('src/renderer/routes/MainRoutes.tsx', [
    'AuthGuard'
  ]);
  console.log(`  ${mainRoutesValid ? '✅' : '❌'} src/renderer/routes/MainRoutes.tsx (AuthGuard added)`);
  if (!mainRoutesValid) allValid = false;
  
  // Check AuthLogin.tsx for integration
  const loginValid = checkFileContent('src/renderer/views/pages/authentication/auth-forms/AuthLogin.tsx', [
    'window.authAPI.login',
    'ErrorHandler'
  ]);
  console.log(`  ${loginValid ? '✅' : '❌'} src/renderer/views/pages/authentication/auth-forms/AuthLogin.tsx (integrated)`);
  if (!loginValid) allValid = false;
  
  // Check AuthRegister.tsx for integration
  const registerValid = checkFileContent('src/renderer/views/pages/authentication/auth-forms/AuthRegister.tsx', [
    'window.authAPI.register',
    'full_name',
    'phone',
    'app_code',
    'password_confirmation'
  ]);
  console.log(`  ${registerValid ? '✅' : '❌'} src/renderer/views/pages/authentication/auth-forms/AuthRegister.tsx (integrated)`);
  if (!registerValid) allValid = false;
  
  console.log('\n🔧 Checking core functionality:');
  
  // Check ApiConfig implementation
  const apiConfigValid = checkFileContent('src/config/ApiConfig.ts', [
    'class ApiConfig',
    'bearerToken',
    'sessionId',
    'getApiUrl',
    'getAuthHeader'
  ]);
  console.log(`  ${apiConfigValid ? '✅' : '❌'} ApiConfig implementation`);
  if (!apiConfigValid) allValid = false;
  
  // Check AuthenticationService implementation
  const authServiceValid = checkFileContent('src/services/AuthenticationService.ts', [
    'class AuthenticationService',
    'login',
    'register',
    'logout',
    'refreshToken',
    'syncSession'
  ]);
  console.log(`  ${authServiceValid ? '✅' : '❌'} AuthenticationService implementation`);
  if (!authServiceValid) allValid = false;
  
  // Check HttpClientService implementation
  const httpClientValid = checkFileContent('src/services/HttpClientService.ts', [
    'class HttpClientService',
    'postFormData',
    'createFormData',
    'getAuthHeader'
  ]);
  console.log(`  ${httpClientValid ? '✅' : '❌'} HttpClientService implementation`);
  if (!httpClientValid) allValid = false;
  
  // Check Redux auth slice
  const authSliceValid = checkFileContent('src/renderer/store/slices/authSlice.ts', [
    'createSlice',
    'loginUser',
    'registerUser',
    'logoutUser',
    'AuthenticationState'
  ]);
  console.log(`  ${authSliceValid ? '✅' : '❌'} Redux auth slice implementation`);
  if (!authSliceValid) allValid = false;
  
  // Check error handling
  const errorHandlerValid = checkFileContent('src/utils/errorHandler.ts', [
    'class ErrorHandler',
    'AppError',
    'AuthenticationError',
    'getUserFriendlyMessage'
  ]);
  console.log(`  ${errorHandlerValid ? '✅' : '❌'} Error handling implementation`);
  if (!errorHandlerValid) allValid = false;
  
  console.log('\n📋 Implementation Summary:');
  
  const features = [
    'API Configuration with environment support',
    'HTTP Client with multipart form data support',
    'Authentication Service (login, register, logout, refresh, sync)',
    'Token Storage with Electron secure storage',
    'Authentication Controller with business logic',
    'IPC Handlers for main-renderer communication',
    'Redux state management for authentication',
    'Route guards (AuthGuard, GuestGuard)',
    'Enhanced login/register forms',
    'Comprehensive error handling',
    'Environment configuration system',
    'Unit and integration tests'
  ];
  
  features.forEach(feature => {
    console.log(`  ✅ ${feature}`);
  });
  
  console.log('\n🎯 Next Steps:');
  console.log('  1. Run tests: npm test');
  console.log('  2. Start the application: npm start');
  console.log('  3. Test authentication flow in the UI');
  console.log('  4. Configure API endpoints for your environment');
  console.log('  5. Test with real API endpoints');
  
  if (allValid) {
    console.log('\n🎉 Authentication implementation validation PASSED!');
    console.log('   All required files and integrations are in place.');
    return true;
  } else {
    console.log('\n❌ Authentication implementation validation FAILED!');
    console.log('   Some files or integrations are missing or incomplete.');
    return false;
  }
}

// Run validation
const isValid = validateImplementation();
process.exit(isValid ? 0 : 1);
