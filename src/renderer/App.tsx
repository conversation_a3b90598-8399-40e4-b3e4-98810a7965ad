import { useSelector } from 'react-redux';
import { RouterProvider } from 'react-router-dom';

import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, StyledEngineProvider } from '@mui/material';

import router from './routes';

import NavigationScroll from './layout/NavigationScroll';
import themes from './themes';
import AlertProvider from './hooks/AlertContext';
import { ModalProvider } from './hooks/ConfirmModalContext';

function App() {
  const customization = useSelector((state: any) => state.customization);
  const muiTheme = themes(customization);

  return (
    <StyledEngineProvider injectFirst>
      <ThemeProvider theme={muiTheme}>
        <CssBaseline />
        <AlertProvider>
          <ModalProvider>
            <NavigationScroll>
              <RouterProvider router={router} />
            </NavigationScroll>
          </ModalProvider>
        </AlertProvider>
      </ThemeProvider>
    </StyledEngineProvider>
  );
}

export default App;
