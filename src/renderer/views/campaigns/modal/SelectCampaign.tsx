import { ChangeEvent, useState } from 'react';
import {
  Box,
  useMediaQuery,
  useTheme,
  Dialog,
  DialogContent,
  DialogActions,
  Typography,
  Stack,
  Divider,
  IconButton,
  Tooltip,
  RadioGroup,
  FormControlLabel,
  Radio,
  Card,
  CardContent,
  alpha,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import PostAddIcon from '@mui/icons-material/PostAdd';
import CommentIcon from '@mui/icons-material/Comment';
import PersonIcon from '@mui/icons-material/Person';
import ButtonSubmit from '../../../components/buttons/ButtonSubmit';
import { CampaignType } from '../CampaignConfig';

interface CampaignModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (campaignType: CampaignType) => void;
}

const campaignOptions = [
  {
    value: 'post',
    label: 'Đăng bài nhóm',
    description: 'Tự động đăng bài lên các nhóm Facebook',
    icon: PostAddIcon,
    color: '#1976d2',
  },
  {
    value: 'comment',
    label: 'Đăng bình luận',
    description: 'Tự động bình luận trên các bài viết',
    icon: CommentIcon,
    color: '#388e3c',
  },
  {
    value: 'profile',
    label: 'Đăng bài trang cá nhân',
    description: 'Đăng bài lên trang cá nhân Facebook',
    icon: PersonIcon,
    color: '#f57c00',
  },
];

function FormSelectCampaign({ open, onClose, onSubmit }: CampaignModalProps) {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const [selected, setSelected] = useState<CampaignType>('post');

  const handleClose = () => {
    setSelected('post');
    onClose();
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSelected(value as CampaignType);
  };

  const handleSubmit = () => {
    onSubmit(selected);
    handleClose();
  };

  return (
    <Dialog
      fullScreen={fullScreen}
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 3,
            overflow: 'hidden',
            minHeight: '50vh',
          },
        },
      }}
    >
      {/* Modern Header Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main || '#1976d2'} 0%, ${theme.palette.primary.dark || '#1565c0'} 100%)`,
          color: 'white',
          p: 3,
        }}
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Box>
            <Typography
              variant="h4"
              sx={{ fontWeight: 600, mb: 1, color: 'white' }}
            >
              Chọn loại chiến dịch
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Lựa chọn loại chiến dịch phù hợp với mục tiêu của bạn
            </Typography>
          </Box>
          <Tooltip title="Đóng">
            <IconButton
              onClick={handleClose}
              sx={{
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Tooltip>
        </Stack>
      </Box>

      <DialogContent sx={{ p: 3 }}>
        <RadioGroup value={selected} onChange={handleChange} sx={{ gap: 2 }}>
          {campaignOptions.map((option) => {
            const IconComponent = option.icon;
            const isSelected = selected === option.value;

            return (
              <Card
                key={option.value}
                sx={{
                  border: `2px solid ${isSelected ? option.color : alpha(theme.palette.grey[300] || '#e0e0e0', 0.5)}`,
                  borderRadius: 2,
                  transition: 'all 0.2s ease-in-out',
                  cursor: 'pointer',
                  '&:hover': {
                    borderColor: option.color,
                    boxShadow: `0 4px 12px ${alpha(option.color, 0.2)}`,
                    transform: 'translateY(-2px)',
                  },
                  ...(isSelected && {
                    backgroundColor: alpha(option.color, 0.04),
                    boxShadow: `0 4px 12px ${alpha(option.color, 0.2)}`,
                  }),
                }}
                onClick={() => setSelected(option.value as CampaignType)}
              >
                <CardContent sx={{ p: 3 }}>
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <FormControlLabel
                      value={option.value}
                      control={
                        <Radio
                          sx={{
                            color: alpha(option.color, 0.6),
                            '&.Mui-checked': {
                              color: option.color,
                            },
                          }}
                        />
                      }
                      label=""
                      sx={{ m: 0 }}
                    />

                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: 2,
                        backgroundColor: alpha(option.color, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: option.color,
                      }}
                    >
                      <IconComponent sx={{ fontSize: 24 }} />
                    </Box>

                    <Box sx={{ flex: 1 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 600,
                          color: isSelected ? option.color : 'text.primary',
                          mb: 0.5,
                        }}
                      >
                        {option.label}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ lineHeight: 1.4 }}
                      >
                        {option.description}
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            );
          })}
        </RadioGroup>
      </DialogContent>

      <Divider />

      <DialogActions
        sx={{
          p: 3,
          backgroundColor: alpha(theme.palette.grey[50] || '#fafafa', 0.5),
          gap: 2,
        }}
      >
        <ButtonSubmit
          isSubmitting={false}
          onClick={handleSubmit}
          variant="contained"
          sx={{
            borderRadius: 2,
            px: 4,
            py: 1,
            minWidth: 120,
          }}
        >
          Tiếp tục
        </ButtonSubmit>
      </DialogActions>
    </Dialog>
  );
}

export default FormSelectCampaign;
