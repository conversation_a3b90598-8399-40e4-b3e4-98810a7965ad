import { useState, useMemo, useEffect } from 'react';
import {
  Dialog,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Checkbox,
  DialogContent,
  DialogActions,
  useMediaQuery,
  useTheme,
  Box,
  Typography,
  Stack,
  Divider,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
  alpha,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import PersonIcon from '@mui/icons-material/Person';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import IndeterminateCheckBoxIcon from '@mui/icons-material/IndeterminateCheckBox';
import { IFBUserResponse } from '../../../../interfaces/IFacebookUser';
import { Campaign } from '../../../../interfaces/Campaigns';
import { useAlert } from '../../../hooks/AlertContext';
import ButtonSubmit from '../../../components/buttons/ButtonSubmit';
import useCampaignRun from '../../../hooks/useCampaignRun';
import { useCampaignContext } from '../context';
import { useNavigate } from 'react-router';

interface AccountFormProps {
  open: boolean;
  onClose: () => void;
  campaign?: Campaign | null;
}
/**
 * ADD EDIT ACCOUNT FORM
 * @param param0
 * @returns
 */
function FormSelectAccount({ open, onClose, campaign }: AccountFormProps) {
  const navigate = useNavigate();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // const { availableUsers } = useCampaignRun();
  const [availableUsers, setAvailableUsers] = useState<IFBUserResponse[]>([]);

  const { updateCampaign } = useCampaignContext();
  const [selectedUsers, setSelectedUsers] = useState<IFBUserResponse[]>([]);
  const [isSubmitting, setSubmitting] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { showAlert } = useAlert();

  async function fetchActiveUsers() {
    const response = await window.account.getUserByStatus('active');
    const users = Array.isArray(response) ? response : [];
    return users;
  }

  useEffect(() => {
    const loadActiveUsers = async () => {
      try {
        const response = await fetchActiveUsers();
        setAvailableUsers(response);
      } catch (error) {
        console.error('Failed to fetch active users:', error);
      }
    };

    loadActiveUsers();
  }, [campaign]);

  // Filtered users based on search query
  const filteredUsers = useMemo(() => {
    if (!searchQuery) return availableUsers;
    return availableUsers.filter(
      (user) =>
        user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.category_name?.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [availableUsers, searchQuery]);

  // Check if all filtered users are selected
  const isAllSelected =
    filteredUsers.length > 0 &&
    filteredUsers.every((user) => selectedUsers.some((u) => u.id === user.id));

  // Check if some (but not all) filtered users are selected
  const isIndeterminate =
    filteredUsers.some((user) => selectedUsers.some((u) => u.id === user.id)) &&
    !isAllSelected;

  const toggleUser = (user: IFBUserResponse) => {
    setSelectedUsers((prev) =>
      prev.some((u) => u.id === user.id)
        ? prev.filter((u) => u.id !== user.id)
        : [...prev, user],
    );
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      // Add all filtered users that aren't already selected
      const newUsers = filteredUsers.filter(
        (user) => !selectedUsers.some((u) => u.id === user.id),
      );
      setSelectedUsers((prev) => [...prev, ...newUsers]);
    } else {
      // Remove all filtered users from selection
      const filteredUserIds = filteredUsers.map((user) => user.id);
      setSelectedUsers((prev) =>
        prev.filter((user) => !filteredUserIds.includes(user.id)),
      );
    }
  };

  const handleClose = () => {
    try {
      setSubmitting(false);
      setSearchQuery('');
      setSelectedUsers([]);
      onClose();
    } catch (error) {
      console.error('Error closing modal:', error);
      // Force close even if there's an error
      onClose();
    }
  };

  const handleConfirmRunCampaign = async () => {
    if (!campaign || !campaign.id) {
      showAlert('Không tìm thấy thông tin chiến dịch', 'error');
      return;
    }

    if (selectedUsers.length === 0) {
      showAlert('Vui lòng chọn ít nhất một tài khoản', 'warning');
      return;
    }

    try {
      setSubmitting(true);

      // Update campaign status to running immediately for UI feedback
      updateCampaign(campaign.id, { ...campaign, status: 'running' });

      handleClose();

      navigate(`/campaign/detail/${campaign.id}`);
      await window.facebookWeb.createUserpost(selectedUsers, campaign.id);

      // Start the campaign with selected users
      await window.facebookWeb.start(selectedUsers, campaign.id);

      // Fetch updated campaign data from server
      const { data } = await window.Campaigns.getCampaign(campaign.id);
      if (data) {
        updateCampaign(campaign.id, data);
      }
    } catch (error) {
      console.error('Error running campaign:', error);
      // Revert campaign status if there was an error
      updateCampaign(campaign.id, { ...campaign, status: 'stopped' });
      showAlert('Có lỗi xảy ra khi chạy chiến dịch', 'error');
    } finally {
      setSubmitting(false);
      handleClose();
    }
  };

  return (
    <Dialog
      fullScreen={fullScreen}
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 3,
            overflow: 'hidden',
            minHeight: '60vh',
          },
        },
      }}
    >
      {/* Modern Header Section */}
      <Box
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main || '#1976d2'} 0%, ${theme.palette.primary.dark || '#1565c0'} 100%)`,
          color: 'white',
          p: 3,
        }}
      >
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Box>
            <Typography
              variant="h4"
              sx={{ fontWeight: 600, mb: 1, color: 'white' }}
            >
              Chọn tài khoản để chạy chiến dịch
            </Typography>
            <Typography variant="body2" sx={{ color: 'white' }}>
              {campaign?.name && `Chiến dịch: ${campaign.name}`}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.8, mt: 0.5 }}>
              {filteredUsers.length} tài khoản khả dụng • {selectedUsers.length}{' '}
              đã chọn
            </Typography>
          </Box>
          <Tooltip title="Đóng">
            <IconButton
              onClick={handleClose}
              sx={{
                color: 'white',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              <CloseIcon />
            </IconButton>
          </Tooltip>
        </Stack>
      </Box>

      {/* Search and Filter Section */}
      <Box
        sx={{
          p: 2,
          backgroundColor: alpha(theme.palette.grey[50] || '#fafafa', 0.8),
        }}
      >
        <Stack direction="row" spacing={2} alignItems="center">
          <TextField
            placeholder="Tìm kiếm tài khoản..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            size="small"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
              },
            }}
            sx={{
              flexGrow: 1,
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                backgroundColor: 'white',
              },
            }}
          />
          <Chip
            label={`${selectedUsers.length} đã chọn`}
            color={selectedUsers.length > 0 ? 'primary' : 'default'}
            variant={selectedUsers.length > 0 ? 'filled' : 'outlined'}
          />
        </Stack>
      </Box>

      {/* Bulk Selection Header */}
      {filteredUsers.length > 0 && (
        <Box
          sx={{
            p: 2,
            backgroundColor: alpha(
              theme.palette.primary.main || '#1976d2',
              0.04,
            ),
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Stack direction="row" alignItems="center" spacing={2}>
            <Checkbox
              checked={isAllSelected}
              indeterminate={isIndeterminate}
              onChange={(e) => handleSelectAll(e.target.checked)}
              icon={<CheckBoxOutlineBlankIcon />}
              checkedIcon={<CheckBoxIcon />}
              indeterminateIcon={<IndeterminateCheckBoxIcon />}
            />
            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
              {isAllSelected ? 'Bỏ chọn tất cả' : 'Chọn tất cả'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              ({filteredUsers.length} tài khoản)
            </Typography>
          </Stack>
        </Box>
      )}

      <DialogContent sx={{ p: 0, flex: 1 }}>
        {filteredUsers.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              py: 8,
              px: 3,
            }}
          >
            <PersonIcon sx={{ fontSize: 64, color: 'text.disabled', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {searchQuery
                ? 'Không tìm thấy tài khoản'
                : 'Không có tài khoản khả dụng'}
            </Typography>
            <Typography
              variant="body2"
              color="text.disabled"
              textAlign="center"
            >
              {searchQuery
                ? 'Thử thay đổi từ khóa tìm kiếm hoặc xóa bộ lọc'
                : 'Vui lòng thêm tài khoản trước khi chạy chiến dịch'}
            </Typography>
          </Box>
        ) : (
          <List sx={{ py: 0 }}>
            {filteredUsers.map((user, index) => (
              <ListItem
                key={user.id}
                disablePadding
                sx={{
                  borderBottom:
                    index < filteredUsers.length - 1
                      ? `1px solid ${alpha(theme.palette.divider || '#e0e0e0', 0.5)}`
                      : 'none',
                }}
              >
                <ListItemButton
                  onClick={() => toggleUser(user)}
                  sx={{
                    py: 2,
                    px: 3,
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      backgroundColor: alpha(
                        theme.palette.primary.main || '#1976d2',
                        0.04,
                      ),
                    },
                  }}
                >
                  <Checkbox
                    checked={selectedUsers.some((u) => u.id === user.id)}
                    tabIndex={-1}
                    disableRipple
                    sx={{
                      color: theme.palette.primary.main || '#1976d2',
                      '&.Mui-checked': {
                        color: theme.palette.primary.main || '#1976d2',
                      },
                    }}
                  />
                  <Avatar
                    sx={{
                      width: 40,
                      height: 40,
                      mr: 2,
                      backgroundColor: alpha(
                        theme.palette.primary.main || '#1976d2',
                        0.1,
                      ),
                      color: theme.palette.primary.main || '#1976d2',
                    }}
                  >
                    {user.username.charAt(0).toUpperCase()}
                  </Avatar>
                  <ListItemText
                    primary={
                      <Typography
                        component="span"
                        variant="subtitle1"
                        sx={{ fontWeight: 500 }}
                      >
                        {user.username}
                      </Typography>
                    }
                    secondary={
                      <Stack
                        direction="row"
                        spacing={1}
                        alignItems="center"
                        sx={{ mt: 0.5 }}
                      >
                        <Chip
                          label={user.category_name || 'Không có danh mục'}
                          size="small"
                          variant="outlined"
                          sx={{
                            fontSize: '0.75rem',
                            height: 20,
                          }}
                        />
                        {user.status && (
                          <Chip
                            label={
                              user.status === 'active'
                                ? 'Hoạt động'
                                : 'Không hoạt động'
                            }
                            size="small"
                            color={
                              user.status === 'active' ? 'success' : 'default'
                            }
                            sx={{
                              fontSize: '0.75rem',
                              height: 20,
                            }}
                          />
                        )}
                      </Stack>
                    }
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        )}
      </DialogContent>

      <Divider />

      <DialogActions
        sx={{
          p: 3,
          backgroundColor: alpha(theme.palette.grey[50] || '#fafafa', 0.5),
          gap: 2,
        }}
      >
        <ButtonSubmit
          isSubmitting={isSubmitting}
          onClick={handleConfirmRunCampaign}
          variant="contained"
          disabled={selectedUsers.length === 0}
          sx={{
            borderRadius: 2,
            px: 4,
            py: 1,
            minWidth: 120,
          }}
        >
          Chạy chiến dịch ({selectedUsers.length})
        </ButtonSubmit>
      </DialogActions>
    </Dialog>
  );
}

FormSelectAccount.defaultProps = {
  campaign: undefined,
};

export default FormSelectAccount;
