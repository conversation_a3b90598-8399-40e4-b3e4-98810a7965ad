/* eslint-disable react/no-array-index-key */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useCallback, useState } from 'react';
import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TablePagination from '@mui/material/TablePagination';
import { Box, useTheme, alpha } from '@mui/material';
import { emptyRows, useTable } from '../../../components/table/TableConfig';
import {
  TableHeadCustom,
  TableEmptyRows,
  EmptyTable,
  TableRowCustom,
} from '../../../components/table';
import { Campaign } from '../../../../interfaces/Campaigns';
import { useCampaignContext } from '../context';
import FormSelectAccount from '../modal/SelectAccount';
import FormSelectCampaign from '../modal/SelectCampaign';
import { CampaignType } from '../CampaignConfig';
import ConfirmModal from '../../../components/modals/ConfirmModal';
import CampaignTableSkeleton from '../../../components/skeletons/CampaignTableSkeleton';
import { getCampaignTypeLabel } from '../../../../utils/campaignStatus';

// Refactored components
import CampaignTableHeader from '../components/CampaignTableHeader';
import SearchAndFilter from '../../../components/SearchAndFilter';
import TableInfoHeader from '../../../components/TableInfoHeader';
import { getCampaignTableColumns } from '../components/CampaignTableColumns';
import {
  getCampaignRunStopAction,
  getStandardCampaignActions,
} from '../components/CampaignActions';

// Custom hooks
import { useCampaignActions } from '../hooks/useCampaignActions';
import { useCampaignSearch } from '../hooks/useCampaignSearch';

type CampaignListProps = {
  type?: CampaignType;
};

function CampaignList({ type }: CampaignListProps) {
  const theme = useTheme();
  const { loading, campaigns } = useCampaignContext();

  // Debug: Track campaigns changes
  React.useEffect(() => {
    console.log(`CampaignList: Campaigns updated, count: ${campaigns.length}`);
  }, [campaigns]);

  // Custom hooks for business logic
  const { searchQuery, setSearchQuery, filteredCampaigns } = useCampaignSearch({
    type,
  });

  // Debug: Track filtered campaigns changes
  React.useEffect(() => {
    console.log(
      `CampaignList: Filtered campaigns updated, count: ${filteredCampaigns.length}`,
    );
  }, [filteredCampaigns]);
  const {
    selectedCampaign,
    runningCampaigns,
    stoppingCampaigns,
    openAccountModal,
    openCampaignModal,
    handleClose,
    onDelete,
    handleSelectCampaign,
    runCampaign,
    stopCampaign,
    handleCampaign,
    handleBulkDelete,
  } = useCampaignActions({ type });

  // Table configuration
  const campaignColumns = getCampaignTableColumns();

  // Enhanced UI state for confirmation dialog
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
  }>({
    open: false,
    title: '',
    message: '',
    onConfirm: () => {},
  });
  // Table state management
  const {
    page,
    order,
    orderBy,
    rowsPerPage,
    selected,
    sortedData,
    totalRows,
    onSort,
    onSelectAllRows,
    onSelectRow,
    onChangePage,
    onChangeRowsPerPage,
  } = useTable({ data: filteredCampaigns, total: filteredCampaigns.length });

  // Enhanced functionality helpers
  const handleConfirmDialog = useCallback(
    (title: string, message: string, onConfirm: () => void) => {
      setConfirmDialog({
        open: true,
        title,
        message,
        onConfirm,
      });
    },
    [],
  );

  const handleCloseConfirmDialog = useCallback(() => {
    setConfirmDialog((prev) => ({ ...prev, open: false }));
  }, []);

  // Bulk actions
  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked) {
        const allIds = filteredCampaigns.map((campaign) => campaign.id);
        onSelectAllRows(true, allIds);
      } else {
        onSelectAllRows(false, []);
      }
    },
    [filteredCampaigns, onSelectAllRows],
  );

  // Bulk delete handler using the refactored hook
  const handleBulkDeleteClick = useCallback(() => {
    if (selected.length === 0) return;

    handleConfirmDialog(
      'Xóa chiến dịch',
      `Bạn có chắc chắn muốn xóa ${selected.length} chiến dịch đã chọn?`,
      async () => {
        console.log('Starting bulk delete for selected campaigns:', selected);
        const success = await handleBulkDelete(
          selected.map((id) => id.toString()),
        );

        // Only clear selection if deletion was successful
        if (success) {
          console.log('Bulk delete successful, clearing selection');
          onSelectAllRows(false, []);
        } else {
          console.warn(
            'Bulk delete failed or partially failed, keeping selection',
          );
          // Keep selection so user can retry if needed
        }
      },
    );
  }, [selected, handleConfirmDialog, handleBulkDelete, onSelectAllRows]);

  // Generate action columns using refactored components
  const getActionsForCampaign = (campaign: Campaign) => {
    const { id, status } = campaign;
    const isDraft = status === 'draft';
    const isStopped = status === 'stopped';
    // Ensure stopped campaigns are not considered running
    const isRunning =
      !isStopped && (runningCampaigns[id] || status === 'running');
    const isDone = status === 'done';
    const isStopping = stoppingCampaigns[id];

    console.log(`Campaign ${id} action states:`, {
      status,
      isDraft,
      isStopped,
      isRunning,
      isDone,
      isStopping,
      runningState: runningCampaigns[id],
    });

    const runStopAction = getCampaignRunStopAction({
      campaign,
      isDraft,
      isStopped,
      isRunning,
      isDone,
      isStopping,
      onRun: runCampaign,
      onStop: stopCampaign,
    });

    const standardActions = getStandardCampaignActions({
      onEdit: handleCampaign,
      onDelete,
    });

    return [runStopAction, ...standardActions];
  };

  if (loading) {
    return <CampaignTableSkeleton theme={theme} />;
  }

  console.log('CampaignTable: Rendering table with data:', filteredCampaigns);

  return (
    <Box>
      {/* Campaign Table Header */}
      <CampaignTableHeader
        type={type}
        selectedCount={selected.length}
        onAdd={() => handleCampaign()}
        onBulkDelete={handleBulkDeleteClick}
      />
      {/* Search and Filter Section */}
      <SearchAndFilter
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        placeholder="Tìm kiếm chiến dịch..."
      />
      {/* Modern Table Card */}
      <Card
        elevation={2}
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        {/* Enhanced Table Header */}
        <TableInfoHeader
          title={`Danh sách chiến dịch ${type ? `(${getCampaignTypeLabel(type)})` : ''}`}
          totalCount={filteredCampaigns.length}
          selectedCount={selected.length}
          onRefresh={() => window.location.reload()}
        />

        <TableContainer
          sx={{
            // Custom compact horizontal scrollbar
            '&::-webkit-scrollbar': {
              height: 8,
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: alpha(theme.palette.grey[300] || '#e0e0e0', 0.5),
              borderRadius: 4,
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: alpha(theme.palette.grey[500] || '#9e9e9e', 0.8),
              borderRadius: 4,
              '&:hover': {
                backgroundColor: theme.palette.grey[600] || '#757575',
              },
            },
            '&::-webkit-scrollbar-corner': {
              backgroundColor: 'transparent',
            },
            // Firefox scrollbar styling
            scrollbarWidth: 'thin',
            scrollbarColor: `${alpha(theme.palette.grey[500] || '#9e9e9e', 0.8)} ${alpha(theme.palette.grey[300] || '#e0e0e0', 0.5)}`,
          }}
        >
          <Table
            sx={{
              width: '100%',
              '& .MuiTableHead-root': {
                backgroundColor: alpha(
                  theme.palette.grey[50] || '#fafafa',
                  0.8,
                ),
              },
              '& .MuiTableHead-root .MuiTableCell-root': {
                fontWeight: 600,
                fontSize: '0.75rem',
                letterSpacing: '0.5px',
                color: theme.palette.text.secondary,
                borderBottom: `2px solid ${theme.palette.divider}`,
              },
              '& .MuiTableBody-root .MuiTableRow-root': {
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  backgroundColor: alpha(
                    theme.palette.primary.main || '#1976d2',
                    0.04,
                  ),
                  transform: 'translateY(-1px)',
                  boxShadow: `0 4px 8px ${alpha(theme.palette.common.black || '#000000', 0.1)}`,
                },
                '&.Mui-selected': {
                  backgroundColor: alpha(
                    theme.palette.primary.main || '#1976d2',
                    0.08,
                  ),
                  '&:hover': {
                    backgroundColor: alpha(
                      theme.palette.primary.main || '#1976d2',
                      0.12,
                    ),
                  },
                },
              },
            }}
          >
            <TableHeadCustom
              columns={campaignColumns}
              order={order}
              orderBy={orderBy}
              rowCount={totalRows}
              numSelected={selected.length}
              onSort={onSort}
              onSelectAllRows={(checked) =>
                onSelectAllRows(
                  checked,
                  sortedData.map((item) => item.id),
                )
              }
            />
            <TableBody>
              {sortedData.map((item) => {
                const actions = getActionsForCampaign(item);

                return (
                  <TableRowCustom
                    key={item.id}
                    item={item}
                    columns={campaignColumns as any}
                    actions={actions as any}
                    selected={selected.includes(item.id)}
                    onSelect={onSelectRow}
                  />
                );
              })}
              <TableEmptyRows
                height={68}
                emptyRows={emptyRows(page, rowsPerPage, campaigns.length)}
              />
              {totalRows === 0 && <EmptyTable />}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          component="div"
          page={page}
          count={totalRows}
          rowsPerPage={rowsPerPage}
          onPageChange={onChangePage}
          rowsPerPageOptions={[10, 25, 50]}
          onRowsPerPageChange={onChangeRowsPerPage}
          labelRowsPerPage="Số hàng mỗi trang:"
          labelDisplayedRows={({ from, to, count }) =>
            `Đang hiển thị ${from} đến ${to} của ${count} hàng`
          }
        />
      </Card>
      <FormSelectAccount
        open={openAccountModal}
        onClose={handleClose}
        campaign={selectedCampaign}
      />
      <FormSelectCampaign
        open={openCampaignModal}
        onClose={handleClose}
        onSubmit={handleSelectCampaign}
      />

      {/* Confirmation Dialog */}
      <ConfirmModal
        open={confirmDialog.open}
        onClose={handleCloseConfirmDialog}
        onSubmit={confirmDialog.onConfirm}
        title={confirmDialog.title}
        content={confirmDialog.message}
        actions={{ cancel: 'Hủy', submit: 'Xác nhận' }}
      />
    </Box>
  );
}

CampaignList.defaultProps = {
  type: undefined,
};

export default CampaignList;
