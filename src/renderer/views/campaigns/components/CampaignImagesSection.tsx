import React from 'react';
import {
  Grid,
  Paper,
  Typography,
  Alert,
  Box,
} from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';
import { Image } from '../../../../interfaces/Campaigns';
import DetailInfoCard from '../../../components/DetailInfoCard';

interface CampaignImagesSectionProps {
  images: Image[] | null;
}

/**
 * Campaign images display section
 */
const CampaignImagesSection: React.FC<CampaignImagesSectionProps> = ({
  images,
}) => {
  const toFileUrl = (path: string) => {
    const fixed = path.replace(/\\/g, '/'); // Windows \ → /
    return `file:///${fixed.split('/').map(encodeURIComponent).join('/')}`;
  };

  return (
    <DetailInfoCard
      title={`Hình ảnh (${images?.length || 0})`}
      icon={<ImageIcon />}
      headerColor="#e91e63"
      iconColor="#e91e63"
    >
      {images && images.length > 0 ? (
        <Grid container spacing={2}>
          {images.map((img, index) => (
            <Grid
              size={{ xs: 12, sm: 6, md: 4, lg: 3 }}
              key={img.id || index}
            >
              <Paper
                sx={{
                  p: 1,
                  textAlign: 'center',
                  borderRadius: 2,
                  overflow: 'hidden',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    boxShadow: 3,
                    transform: 'scale(1.02)',
                  },
                }}
              >
                <Box
                  sx={{
                    position: 'relative',
                    width: '100%',
                    height: 150,
                    borderRadius: 1,
                    overflow: 'hidden',
                    backgroundColor: 'grey.100',
                  }}
                >
                  <img
                    src={img.image ? toFileUrl(img.image) : '/placeholder-image.png'}
                    alt={`Ảnh chiến dịch ${index + 1}`}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder-image.png';
                      target.style.backgroundColor = '#f5f5f5';
                      target.style.display = 'flex';
                      target.style.alignItems = 'center';
                      target.style.justifyContent = 'center';
                    }}
                    onLoad={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.opacity = '1';
                    }}
                    loading="lazy"
                  />
                </Box>
                <Typography
                  variant="caption"
                  display="block"
                  sx={{
                    mt: 1,
                    fontWeight: 500,
                    color: 'text.secondary',
                  }}
                >
                  Ảnh {index + 1}
                </Typography>
                {img.image && (
                  <Typography
                    variant="caption"
                    display="block"
                    sx={{
                      fontSize: '0.6rem',
                      color: 'text.disabled',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      maxWidth: '100%',
                    }}
                    title={img.image}
                  >
                    {img.image.split(/[\\/]/).pop() || 'Unknown file'}
                  </Typography>
                )}
              </Paper>
            </Grid>
          ))}
        </Grid>
      ) : (
        <Alert severity="info" icon={<ImageIcon />}>
          Không có hình ảnh nào được thêm vào chiến dịch này.
        </Alert>
      )}
    </DetailInfoCard>
  );
};

export default CampaignImagesSection;
