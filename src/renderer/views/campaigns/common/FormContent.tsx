/* eslint-disable react/no-array-index-key */
import { ChangeEvent, useState, useEffect } from 'react';
import Grid from '@mui/material/Grid';
import {
  Box,
  Card,
  CardMedia,
  IconButton,
  Typography,
  Chip,
  Stack,
  useTheme,
  alpha,
  Fade,
  Tooltip,
  Paper,
  Button,
} from '@mui/material';
import {
  Close as CloseIcon,
  Image as ImageIcon,
  CloudUpload as CloudUploadIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { CampaignConfigProps, InputConfig } from '../CampaignConfig';
import InputText from '../../../components/form/InputText';

import FormWrapper from './FormWrapper';
import {
  formatFileSize,
  getFileName,
  getImagePreviewUrl,
} from '../../../../utils/file';

interface FileWithPath {
  file: File;
  path: string;
}

// Utility function to convert file path to File object for display
const createFileFromPath = async (
  filePath: string,
): Promise<FileWithPath | null> => {
  try {
    // Check if Electron API is available
    if ((window as any).electronAPI?.fs?.readFile) {
      const fileData = await (window as any).electronAPI.fs.readFile(filePath);
      if (fileData) {
        const fileName = filePath.split(/[\\/]/).pop() || 'unknown';
        const file = new File([fileData], fileName, {
          type: `image/${fileName.split('.').pop()?.toLowerCase() || 'png'}`,
        });
        return { file, path: filePath };
      }
    }

    // Fallback: create a placeholder File object
    const fileName = filePath.split(/[\\/]/).pop() || 'unknown';
    const file = new File([''], fileName, {
      type: `image/${fileName.split('.').pop()?.toLowerCase() || 'png'}`,
    });
    return { file, path: filePath };
  } catch (error) {
    console.error(`Error creating file from path ${filePath}:`, error);
    return null;
  }
};

export default function FormContent({
  config,
  onChange,
  errors,
}: CampaignConfigProps) {
  const theme = useTheme();
  const [hoveredImageIndex, setHoveredImageIndex] = useState<number | null>(
    null,
  );
  const [uploadedFiles, setUploadedFiles] = useState<FileWithPath[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize existing images from config.imagePaths when editing
  useEffect(() => {
    const initializeExistingImages = async () => {
      if (!isInitialized && config.imagePaths && config.imagePaths.length > 0) {
        console.log('Initializing existing images:', config.imagePaths);

        const existingFiles: FileWithPath[] = [];

        for (const imagePath of config.imagePaths) {
          const fileWithPath = await createFileFromPath(imagePath);
          if (fileWithPath) {
            existingFiles.push(fileWithPath);
          }
        }

        console.log('Loaded existing files:', existingFiles);
        setUploadedFiles(existingFiles);
        setIsInitialized(true);
      }
    };

    initializeExistingImages();
  }, [config.imagePaths, isInitialized]);

  const inputs: InputConfig[] = [
    {
      name: 'name',
      label: 'Tên chiến dịch',
      type: 'text',
      value: config.name,
      errorMessage: errors?.name,
    },
    {
      name: 'message',
      label: 'Nội dung',
      type: 'textarea',
      value: config.message,
      multiline: true,
      rows: 4,
      errorMessage: errors?.message,
    },
  ];

  // Handle image deletion
  const handleDeleteImage = (indexToDelete: number) => {
    const updatedFiles = uploadedFiles.filter(
      (_, index) => index !== indexToDelete,
    );
    const updatedPaths = updatedFiles.map((fileWithPath) => fileWithPath.path);
    setUploadedFiles(updatedFiles);
    onChange('imagePaths', updatedPaths);

    console.log('Deleted image at index:', indexToDelete);
    console.log('Updated files:', updatedFiles);
    console.log('Updated paths:', updatedPaths);
  };

  // Handle new image uploads using Electron dialog
  const handleImageUpload = async () => {
    try {
      // Check if Electron API is available
      if (!(window as any).electronAPI?.dialog?.showOpenDialog) {
        console.warn(
          'Electron dialog API not available, falling back to file input',
        );
        handleFallbackImageUpload();
        return;
      }

      // Use Electron's dialog API to get file paths
      const result = await (window as any).electronAPI.dialog.showOpenDialog({
        properties: ['openFile', 'multiSelections'],
        filters: [
          {
            name: 'Images',
            extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
          },
        ],
      });

      if (result && !result.canceled && result.filePaths.length > 0) {
        // Create File objects from the selected paths
        const newFilesWithPaths: FileWithPath[] = [];

        for (const filePath of result.filePaths) {
          try {
            // Read file data using Electron's fs API
            const fileData = await (window as any).electronAPI.fs.readFile(
              filePath,
            );
            if (fileData) {
              const fileName = filePath.split(/[\\/]/).pop() || 'unknown';
              const file = new File([fileData], fileName, {
                type: `image/${fileName.split('.').pop()?.toLowerCase() || 'png'}`,
              });

              newFilesWithPaths.push({
                file,
                path: filePath,
              });
            }
          } catch (error) {
            console.error(`Error reading file ${filePath}:`, error);
          }
        }

        const updatedFiles = [...uploadedFiles, ...newFilesWithPaths];
        const updatedPaths = updatedFiles.map(
          (fileWithPath) => fileWithPath.path,
        );

        console.log('Updated files:', updatedFiles);
        console.log('Updated paths:', updatedPaths);

        setUploadedFiles(updatedFiles);
        onChange('imagePaths', updatedPaths);
      }
    } catch (error) {
      console.error('Error selecting files:', error);
      // Fallback to regular file input if Electron API is not available
      handleFallbackImageUpload();
    }
  };

  // Fallback method for regular file input (without full paths)
  const handleFallbackImageUpload = (e?: ChangeEvent<HTMLInputElement>) => {
    // If no event provided, trigger the file input click
    if (!e) {
      const fileInput = document.getElementById(
        'fallback-file-input',
      ) as HTMLInputElement;
      if (fileInput) {
        fileInput.click();
      }
      return;
    }

    const files = e?.target?.files ? Array.from(e.target.files) : [];
    if (files.length === 0) return;

    const newFilesWithPaths: FileWithPath[] = files.map((file) => ({
      file,
      path: file.name, // Fallback to just filename if full path not available
    }));

    const updatedFiles = [...uploadedFiles, ...newFilesWithPaths];
    const updatedPaths = updatedFiles.map((fileWithPath) => fileWithPath.path);

    console.log('Fallback - Updated files:', updatedFiles);
    console.log('Fallback - Updated paths:', updatedPaths);

    setUploadedFiles(updatedFiles);
    onChange('imagePaths', updatedPaths);
  };

  return (
    <FormWrapper title="Nội dung" description="Tạo nội dung cho chiến dịch">
      <Grid container spacing={2} m={2}>
        <Grid size={12}>
          {inputs.map((input, index) => (
            <InputText
              key={index}
              id={input.name}
              label={input.label}
              name={input.name}
              type={input.type}
              value={input.value || ''}
              required={input.required}
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                onChange(input.name, e.target.value, input.type)
              }
              multiline={input.multiline}
              rows={input.rows}
              errorMessage={input.errorMessage}
            />
          ))}
        </Grid>
      </Grid>

      {/* Image Upload Section */}
      <Box m={2}>
        <Stack spacing={2}>
          {/* Upload Button */}
          <Button
            variant="contained"
            startIcon={<CloudUploadIcon />}
            onClick={handleImageUpload}
            sx={{
              alignSelf: 'flex-start',
              borderRadius: 2,
              textTransform: 'none',
              fontWeight: 600,
            }}
          >
            Tải hình ảnh
          </Button>

          {/* Fallback file input (hidden) */}
          <input
            type="file"
            accept="image/*"
            multiple
            style={{ display: 'none' }}
            onChange={handleFallbackImageUpload}
            id="fallback-file-input"
          />

          {/* Image Count and Info */}
          {uploadedFiles && uploadedFiles.length > 0 && (
            <Stack direction="row" spacing={1} alignItems="center">
              <Chip
                icon={<ImageIcon />}
                label={`${uploadedFiles.length} hình ảnh đã chọn`}
                color="primary"
                variant="outlined"
                size="small"
              />
              <Typography variant="body2" color="text.secondary">
                Click vào biểu tượng X để xóa hình ảnh
              </Typography>
            </Stack>
          )}

          {/* Image Preview Grid */}
          {uploadedFiles && uploadedFiles.length > 0 ? (
            <Paper
              elevation={1}
              sx={{
                p: 2,
                borderRadius: 2,
                backgroundColor: alpha(
                  theme.palette.grey[50] || '#fafafa',
                  0.5,
                ),
              }}
            >
              <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                Hình ảnh đã chọn
              </Typography>

              <Grid container spacing={2}>
                <AnimatePresence>
                  {uploadedFiles.map(
                    (fileWithPath: FileWithPath, index: number) => (
                      <Grid
                        size={{ xs: 6, sm: 4, md: 3 }}
                        key={`${fileWithPath.file.name}-${index}`}
                      >
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.8 }}
                          transition={{ duration: 0.2 }}
                          whileHover={{ scale: 1.02 }}
                        >
                          <Card
                            sx={{
                              position: 'relative',
                              borderRadius: 2,
                              overflow: 'hidden',
                              transition: 'all 0.2s ease-in-out',
                              '&:hover': {
                                boxShadow: theme.shadows[4],
                                '& .delete-button': {
                                  opacity: 1,
                                },
                              },
                            }}
                            onMouseEnter={() => setHoveredImageIndex(index)}
                            onMouseLeave={() => setHoveredImageIndex(null)}
                          >
                            {/* Image Preview */}
                            <CardMedia
                              component="img"
                              height="120"
                              image={getImagePreviewUrl(
                                fileWithPath.file,
                                fileWithPath.path,
                              )}
                              alt={getFileName(
                                fileWithPath.file,
                                fileWithPath.path,
                              )}
                              sx={{
                                objectFit: 'cover',
                                backgroundColor: theme.palette.grey[100],
                              }}
                            />

                            {/* Delete Button */}
                            <Tooltip title="Xóa hình ảnh">
                              <IconButton
                                className="delete-button"
                                onClick={() => handleDeleteImage(index)}
                                sx={{
                                  position: 'absolute',
                                  top: 4,
                                  right: 4,
                                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                  color: 'white',
                                  opacity: hoveredImageIndex === index ? 1 : 0,
                                  transition: 'opacity 0.2s ease-in-out',
                                  '&:hover': {
                                    backgroundColor: 'rgba(255, 0, 0, 0.8)',
                                  },
                                  width: 28,
                                  height: 28,
                                }}
                              >
                                <CloseIcon sx={{ fontSize: 16 }} />
                              </IconButton>
                            </Tooltip>

                            {/* Image Info */}
                            <Box
                              sx={{
                                p: 1,
                                backgroundColor: 'white',
                              }}
                            >
                              <Typography
                                variant="caption"
                                sx={{
                                  display: 'block',
                                  fontWeight: 500,
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap',
                                }}
                              >
                                {getFileName(
                                  fileWithPath.file,
                                  fileWithPath.path,
                                )}
                              </Typography>
                              <Typography
                                variant="caption"
                                color="text.secondary"
                                sx={{ display: 'block' }}
                              >
                                {formatFileSize(
                                  fileWithPath.file,
                                  fileWithPath.path,
                                )}
                              </Typography>
                            </Box>
                          </Card>
                        </motion.div>
                      </Grid>
                    ),
                  )}
                </AnimatePresence>
              </Grid>
            </Paper>
          ) : (
            /* Empty State */
            <Fade in>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  textAlign: 'center',
                  borderRadius: 2,
                  backgroundColor: alpha(
                    theme.palette.grey[50] || '#fafafa',
                    0.3,
                  ),
                  border: `2px dashed ${alpha(theme.palette.grey[400] || '#bdbdbd', 0.5)}`,
                }}
              >
                <CloudUploadIcon
                  sx={{
                    fontSize: 48,
                    color: theme.palette.grey[400],
                    mb: 1,
                  }}
                />
                <Typography variant="body2" color="text.secondary">
                  Chưa có hình ảnh nào được chọn
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Sử dụng nút &quot;Tải hình ảnh&quot; ở trên để thêm hình ảnh
                </Typography>
              </Paper>
            </Fade>
          )}
        </Stack>
      </Box>
    </FormWrapper>
  );
}
