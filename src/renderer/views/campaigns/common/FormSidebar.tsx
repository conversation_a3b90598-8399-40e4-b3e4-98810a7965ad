import Box from "@mui/material/Box";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";

type Step = {
  index: number,
  label: string,
  color: string,
}

type NavProps = {
  initSteps: Array<Step>,
  currentStep: number;
  goTo: (index: number) => void;
};

function FormSideBar({ initSteps, currentStep, goTo }: NavProps) {
  return (
    <Box
      sx={{
        position: { xs: "absolute", md: "relative" },
        top: { xs: "-5rem", md: 0 },
        left: 0,
        width: { xs: "100%", md: "25%" },
      }}
    >
      <Box
        component="nav"
        sx={{
          py: { xs: 2.5, md: 5 },
          px: { md: 2.5 },
          bgcolor: "inherit",
          height: "100%",
          borderRadius: 1,
          border: 1,
          borderColor: "#4b5563",
        }}
      >
        <List
          sx={{
            display: "flex",
            flexDirection: { xs: "row", md: "column" },
            justifyContent: { xs: "center", md: "flex-start" },
            gap: 1,
            p: 0,
          }}
        >
          {initSteps.map(({ index, label, color }) => (
            <ListItem
              key={index}
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "flex-start",
                p: 0,
              }}
            >
              <Typography
                variant="caption"
                sx={{
                  display: { xs: "none", md: "block" },
                  textTransform: "uppercase",
                  mb: 0.5,
                }}
              >
                {index}
              </Typography>
              <Button
                onClick={() => goTo(index)}
                sx={{
                  p: 0,
                  minWidth: "unset",
                  textTransform: "none",
                  fontSize: { xs: "0.875rem", md: "1rem" },
                  fontWeight: 500,
                  color: currentStep === index ? color : "inherit",
                  textDecoration: currentStep === index ? "underline" : "none",
                  "&:hover": {
                    backgroundColor: "transparent",
                    color: currentStep === index ? color : "primary",
                    textDecoration: currentStep === index ? "underline" : "none",
                  },
                }}
                disableRipple
              >
                {label}
              </Button>
            </ListItem>
          ))}
        </List>
      </Box>
    </Box>
  );
};

export default FormSideBar;