/* eslint-disable react/no-array-index-key */
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { Grid } from '@mui/material';
import InputText from '../../../components/form/InputText';
import { CampaignConfigProps, InputConfig } from '../CampaignConfig';

export default function GeneralConfig({
  config,
  onChange,
}: CampaignConfigProps) {
  const inputs: InputConfig[] = [
    {
      name: 'delay',
      label: 'Khoảng cách giữa mỗi lần đăng (giây)',
      type: 'number',
      value: config.delay,
      required: true,
    },
    {
      name: 'max_post',
      label: 'Mỗi tài khoản đăng tối đa số bài',
      value: config.max_post,
      type: 'number',
      required: true,
    },
  ];

  return (
    <Box m={2}>
      <Typography variant="h5" sx={{ flexGrow: 1 }}>
        <PERSON><PERSON><PERSON> h<PERSON>nh chung
      </Typography>
      <Grid container spacing={2}>
        {inputs.map((item, index) => (
          <Grid key={index} size={{ xs: 12, md: 6 }}>
            <InputText
              id={item.name}
              label={item.label}
              type={item.type}
              name={item.name}
              value={item.value}
              required={item.required}
              onChange={(e) => onChange(item.name, e.target.value, item.type)}
            />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
