/* eslint-disable @typescript-eslint/no-unused-vars */
import { Box } from '@mui/material';
import { Outlet, useLocation, useParams } from 'react-router';
import { useEffect } from 'react';
import { CampaignProvider } from './context';
import CampaignList from './table';
import { CampaignType } from './CampaignConfig';

export default function CampaignWrapper() {
  const { type, id } = useParams<{ type?: CampaignType; id?: string }>();
  const location = useLocation();
  const isAddOrEdit =
    location.pathname.includes('/add') || location.pathname.includes('/edit');

  const isRenderDetailOrAddEdit =
    (type && Number(type) > 0) || isAddOrEdit || (!!id && Number(id) > 0);

  if (isRenderDetailOrAddEdit) {
    // render CampaignDetail or AddEdit
    return (
      <CampaignProvider>
        <Box>
          <Outlet />
        </Box>
      </CampaignProvider>
    );
  }

  return (
    <CampaignProvider>
      <Box>
        {type && <CampaignList type={type === 'all' ? undefined : type} />}
        <Outlet />
      </Box>
    </CampaignProvider>
  );
}
