import { useState, useEffect, useCallback } from 'react';
import { CampaignDetails } from '../../../../interfaces/Campaigns';

interface ProgressLog {
  message: string;
  campaignId: string;
  action?: 'running' | 'done' | undefined;
}

interface UseCampaignDetailReturn {
  campaignDetail: CampaignDetails | null;
  loading: boolean;
  error: string | null;
  logs: ProgressLog[];
  addLog: (newLog: ProgressLog) => void;
  refreshCampaignData: () => Promise<void>;
}

/**
 * Custom hook for campaign detail data management
 */
export const useCampaignDetail = (
  id: string | undefined,
): UseCampaignDetailReturn => {
  const [campaignDetail, setCampaignDetail] = useState<CampaignDetails | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<ProgressLog[]>([]);

  const addLog = (newLog: ProgressLog) => {
    setLogs((prev) => [...prev, newLog]);
  };

  // Function to refresh campaign data - memoized to prevent infinite loops
  const refreshCampaignData = useCallback(async () => {
    if (!id) {
      console.warn('refreshCampaignData called but no campaign ID available');
      return;
    }

    try {
      console.log(`[REFRESH] Starting refresh for campaign ID: ${id}`);
      console.log(
        `[REFRESH] Current campaign status before refresh:`,
        campaignDetail?.Campaign?.status,
      );

      const data = await window.Campaigns.getCampaignDetails(id);

      console.log(`[REFRESH] Received fresh data:`, data?.Campaign?.status);
      setCampaignDetail(data);
      console.log(
        `[REFRESH] Campaign data refreshed successfully for ID: ${id}`,
      );
      console.log(`[REFRESH] New campaign status:`, data?.Campaign?.status);
    } catch (err) {
      console.error(
        `[REFRESH] Error refreshing campaign details for ID ${id}:`,
        err,
      );
      // Don't set error state for refresh failures to avoid disrupting the UI
    }
  }, [id, campaignDetail?.Campaign?.status]);

  // Set up progress listener
  useEffect(() => {
    if (!id) return undefined;

    const cleanup = window.facebookWeb.onProgress((data) => {
      // Add new log to state with proper action type conversion
      const action =
        data.action === 'running' || data.action === 'done'
          ? data.action
          : undefined;
      addLog({
        message: data.message,
        campaignId: data.campaignId,
        action,
      });

      if (data.action === 'stopped') {
        // Handle campaign stopped
        console.log('Campaign stopped:', data.campaignId);
      }

      console.log('Progress message:', data.message);
    });

    return cleanup;
  }, [id]);

  // Fetch campaign detail data
  useEffect(() => {
    if (!id) return;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await window.Campaigns.getCampaignDetails(id);
        setCampaignDetail(data);
      } catch (err) {
        console.error('Error fetching campaign details:', err);
        setError('Không thể tải thông tin chiến dịch. Vui lòng thử lại.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  return {
    campaignDetail,
    loading,
    error,
    logs,
    addLog,
    refreshCampaignData,
  };
};
