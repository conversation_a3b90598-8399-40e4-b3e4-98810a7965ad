import { useState, useEffect, useCallback, useMemo } from 'react';
import { Campaign } from '../../../../interfaces/Campaigns';
import { CampaignType } from '../CampaignConfig';
import { useCampaignContext } from '../context';
import { filterCampaigns } from '../../../../utils/campaignStatus';

interface UseCampaignSearchProps {
  type?: CampaignType;
}

interface UseCampaignSearchReturn {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filteredCampaigns: Campaign[];
  handleSearch: (value: string) => Promise<void>;
}

/**
 * Custom hook for campaign search and filtering functionality
 */
export const useCampaignSearch = ({
  type,
}: UseCampaignSearchProps): UseCampaignSearchReturn => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const { campaigns, getAllCampaigns } = useCampaignContext();

  // Filter campaigns based on search query
  const filteredCampaigns = useMemo(() => {
    return filterCampaigns(campaigns, searchQuery);
  }, [campaigns, searchQuery]);

  // Search handler - only fetch if really needed
  const handleSearch = useCallback(async (value: string) => {
    console.log(`Searching campaigns with query: ${value}`);
    // Only fetch from server if we need fresh data for search
    // For now, we rely on local filtering which is more responsive
    // const response = await window.Campaigns.getAllCampaigns();
    // getAllCampaigns(response);
  }, []);

  // Effect for debounced search
  useEffect(() => {
    if (searchQuery.trim().length > 0) {
      const timeoutId = setTimeout(() => handleSearch(searchQuery), 2000);
      return () => clearTimeout(timeoutId);
    }
  }, [handleSearch, searchQuery]);

  // Effect for loading campaigns by type - only run on mount or type change
  useEffect(() => {
    if (type) {
      console.log(`useCampaignSearch: Loading campaigns for type: ${type}`);
      window.Campaigns.getAllCampaigns(type)
        .then((response) => {
          console.log(
            'useCampaignSearch: About to call getAllCampaigns with:',
            response,
          );
          getAllCampaigns(response);
          return response;
        })
        .catch(() => []);
    }
  }, [type]); // Removed getAllCampaigns from dependencies to prevent re-fetching after local updates

  return {
    searchQuery,
    setSearchQuery,
    filteredCampaigns,
    handleSearch,
  };
};
