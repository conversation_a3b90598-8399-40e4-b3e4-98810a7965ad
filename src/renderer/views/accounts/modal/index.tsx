import React, { useEffect, useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import { Box, SelectChangeEvent, useMediaQuery, useTheme } from '@mui/material';
import { Category, CategoryType } from '../../../../interfaces/Categorys';
import FormSelect from '../../../components/form/FormSelect';
import InputText from '../../../components/form/InputText';
import InputCheckbox from '../../../components/form/InputCheckbox';
import CommonValidation from '../../../../utils/validation';
import { formDataType, formInitValues } from './formConfig';
import { useAccountContext } from '../context';
import { IFBUser } from '../../../../interfaces/IFacebookUser';
import { useAlert } from '../../../hooks/AlertContext';
import { messages } from '../../../../utils/messages';
import ButtonSubmit from '../../../components/buttons/ButtonSubmit';
import InputPassword from '../../../components/form/InputPassword';

/**
 * Generate profile id
 */
function generateProfileId() {
  const now = new Date();
  const profileId =
    now.getFullYear().toString() +
    String(now.getMonth() + 1).padStart(2, '0') +
    String(now.getDate()).padStart(2, '0') +
    String(now.getHours()).padStart(2, '0') +
    String(now.getMinutes()).padStart(2, '0') +
    String(now.getSeconds()).padStart(2, '0');

  return profileId;
}

interface AccountFormProps {
  open: boolean;
  onClose: () => void;
  account?: IFBUser;
}

/**
 * ADD EDIT ACCOUNT FORM
 * @param param0
 * @returns
 */
function AccountForm({ open, onClose, account }: AccountFormProps) {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));

  const { addAccount, updateAccount } = useAccountContext();
  const [categories, setCategories] = useState<Category[]>([]);
  const [selected, setSelected] = useState('');
  const [errors, setErrors] = useState<{
    username?: string;
    password?: string;
  }>({});
  const [isSubmitting, setSubmitting] = useState(false);
  const { showAlert } = useAlert();

  const [formData, setFormData] = useState<formDataType>(formInitValues);

  // fetch categories
  useEffect(() => {
    async function fetchCategories() {
      const result = await window.category.getAllCategorys(
        CategoryType.RESOURCE_TYPE_USER,
      );
      setCategories(result);
    }
    fetchCategories();
  }, []);

  useEffect(() => {
    if (categories.length > 0) {
      const categoryId = categories[0].category_id;
      setSelected(categoryId);
    }
  }, [categories]);

  // if EDIT, set formdata values
  useEffect(() => {
    if (account) {
      setFormData({
        username: account.username,
        password: account.password,
        categoryId: account.categoryId,
      });

      setSelected(account.categoryId);
    }
  }, [account]);

  /**
   * Validate form data
   * @returns boolean
   */
  const validateData = () => {
    const newErrors: { username?: string; password?: string } = {};
    const [errRequired] = CommonValidation.requiredString(formData.username);
    if (errRequired) {
      newErrors.username = errRequired;
    }

    const [errPass] = CommonValidation.requiredString(formData.password);
    if (errPass) {
      newErrors.password = errPass;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    setFormData((prev) => {
      const newData = {
        ...prev,
        [name]: type === 'checkbox' ? checked : value,
      };
      return newData;
    });
  };

  /**
   * UPDATE ACCOUNT
   */
  const handleUpdate = async () => {
    if (!account) {
      return;
    }

    const { username, password } = formData;
    const request: IFBUser = {
      ...account,
      username,
      password,
      categoryId: selected,
    };

    const response = await window.account.updateUser(request);
    if (response.success && response.data) {
      const user = response.data;
      updateAccount(user.id, user); // update state
      showAlert(messages.general.success, 'success');
    } else {
      showAlert(messages.general.error, 'error');
    }
  };

  /**
   * ADD ACOUNT
   */
  const handleAdd = async () => {
    const profileId = generateProfileId();
    const { username, password, isLoginNow } = formData;

    const request = {
      username,
      password,
      categoryId: selected,
      userId: '',
      profileId,
      status: 'inactive',
    };

    try {
      if (isLoginNow) {
        const loginfb = await window.facebookWeb.login({
          username,
          password,
          profileId,
        });
        if (loginfb && loginfb.success && loginfb.userId) {
          request.userId = loginfb.userId;
          request.status = 'active';
        }
      }

      const response = await window.account.createUser(request);
      if (response.data) {
        const { data } = response;
        addAccount(data); // update state

        const message =
          data.status === 'active' ? 'đăng nhập thành công' : 'chưa đăng nhập';
        showAlert(`Tài khoản ${data.username} ${message}`, 'success');
      } else {
        showAlert(messages.general.error, 'error');
      }
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      showAlert(messages.general.error, 'error');
    }
  };

  const handleSubmit = () => {
    if (validateData()) {
      setSubmitting(true);

      if (account) {
        handleUpdate();
      } else {
        handleAdd();
      }

      setTimeout(() => setSubmitting(false), 2000);
      setFormData(formInitValues); // reset form
      onClose();
    }
  };

  const handleClose = () => {
    setFormData(formInitValues); // reset form
    onClose();
  };

  return (
    <Dialog fullScreen={fullScreen} open={open} onClose={onClose}>
      <Box marginX={1} marginY={2}>
        <DialogTitle color="primary" fontWeight={600}>
          {account?.id ? 'Thêm mới tài khoản' : 'Chỉnh sửa tài khoản'}
        </DialogTitle>
        <DialogContent>
          <FormSelect
            id="category"
            name="categoryId"
            label="Danh mục"
            options={categories.map((c: Category) => {
              return {
                value: c.category_id,
                text: c.category_name,
              };
            })}
            value={selected}
            onChange={(e: SelectChangeEvent) =>
              setSelected(e.target.value as string)
            }
            sx={{ mb: 1 }}
          />

          <InputText
            id="username"
            label="Tài khoản"
            type="text"
            name="username"
            value={formData.username}
            errorMessage={errors.username}
            onChange={handleChange}
          />

          <InputPassword
            id="password"
            label="Mật khẩu"
            name="password"
            value={formData.password}
            errorMessage={errors.password}
            onChange={handleChange}
          />

          {formData.checkBox && (
            <InputCheckbox
              label="Đăng nhập Facebook ngay"
              name="isLoginNow"
              checked={formData.isLoginNow ?? true}
              onChange={handleChange}
            />
          )}
        </DialogContent>
        <DialogActions
          sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}
        >
          <Button
            onClick={handleClose}
            variant="outlined"
            sx={{ flex: '0 1 100px' }}
          >
            Hủy
          </Button>
          <ButtonSubmit
            isSubmitting={isSubmitting}
            onClick={handleSubmit}
            variant="contained"
            sx={{ flex: '0 1 100px' }}
          >
            Lưu
          </ButtonSubmit>
        </DialogActions>
      </Box>
    </Dialog>
  );
}

AccountForm.defaultProps = {
  account: undefined,
};

export default AccountForm;
