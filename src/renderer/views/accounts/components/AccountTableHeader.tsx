import React from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Stack,
  useTheme,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

interface AccountTableHeaderProps {
  selectedCount: number;
  onAdd: () => void;
  onBulkDelete: () => void;
}

/**
 * Account table header component with title and action buttons
 */
const AccountTableHeader: React.FC<AccountTableHeaderProps> = ({
  selectedCount,
  onAdd,
  onBulkDelete,
}) => {
  const theme = useTheme();

  return (
    <Paper elevation={1} sx={{ p: 2, mb: 3, borderRadius: 2 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
            Quản lý tài khoản Facebook
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            Quản lý tà<PERSON> kho<PERSON>n Facebook để tự động hóa marketing
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          {selectedCount > 0 && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={onBulkDelete}
              sx={{
                borderColor: theme.palette.error.main,
                color: theme.palette.error.main,
                '&:hover': {
                  color: 'white',
                  borderColor: theme.palette.error.main,
                  backgroundColor: theme.palette.error.main,
                },
              }}
            >
              Xóa ({selectedCount})
            </Button>
          )}
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={onAdd}
            sx={{
              backgroundColor: theme.palette.primary.main,
              color: 'white',
            }}
          >
            Thêm tài khoản
          </Button>
        </Stack>
      </Stack>
    </Paper>
  );
};

export default AccountTableHeader;
