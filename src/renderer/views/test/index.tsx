import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import { useState } from 'react';
import { TextareaAutosize } from '@mui/material';
import MainCard from '../../components/cards/MainCard';

function TestPage() {
  const [message, setMessage] = useState('');
  const filePaths = [
    'C:\\Users\\<USER>\\Downloads\\img1.jpg',
    'C:\\Users\\<USER>\\Downloads\\img2.jpg',
    'C:\\Users\\<USER>\\Downloads\\img3.jpg',
    'C:\\Users\\<USER>\\Downloads\\img4.jpg',
    'C:\\Users\\<USER>\\Downloads\\img5.jpg',
  ]
  const [ result, setResult ] = useState('Ready');

  const handleChangeMessage = (e: any) => {
    setMessage(e.target.value);
  }

  const handlePost = async () => {
    const response = await window.facebookWeb.postGroups('1', ['1099433592113391'], { message, filePaths } );
    if (response.success) {
      setResult('OK');
    }else if(response.error) {
      setResult(response.error);
    }
  }
  
  return <MainCard title="Sample Card">
    <TextareaAutosize minRows={5} style={{width: 500}} onChange={(e) => handleChangeMessage(e)}/>
    <Typography variant="body2">
      {result}
    </Typography>
    <Button onClick={handlePost}>Post</Button>
  </MainCard>
}

export default TestPage;
