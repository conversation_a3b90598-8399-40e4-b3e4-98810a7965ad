/**
 * Authentication Redux Slice
 * Manages authentication state including user session, tokens, and auth flow
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  AuthenticationState,
  User,
  LoginRequest,
  RegisterRequest,
  LoginResponse,
  RegisterResponse,
  LogoutResponse,
} from '../../interfaces/IAuthentication';

// Initial state
const initialState: AuthenticationState = {
  isAuthenticated: false,
  user: null,
  token: null,
  sessionId: null,
  expiresAt: null,
  isLoading: false,
  error: null,
  lastLoginAt: null,
};

// Async thunks for authentication actions
export const loginUser = createAsyncThunk<
  LoginResponse,
  LoginRequest,
  { rejectValue: string }
>(
  'auth/loginUser',
  async (loginData, { rejectWithValue }) => {
    try {
      const result = await window.authAPI.login(loginData);
      if (!result.success) {
        return rejectWithValue(result.error || 'Lo<PERSON> failed');
      }
      return result;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Lo<PERSON> failed');
    }
  }
);

export const registerUser = createAsyncThunk<
  RegisterResponse,
  RegisterRequest,
  { rejectValue: string }
>(
  'auth/registerUser',
  async (registerData, { rejectWithValue }) => {
    try {
      const result = await window.authAPI.register(registerData);
      if (!result.success) {
        return rejectWithValue(result.error || 'Registration failed');
      }
      return result;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Registration failed');
    }
  }
);

export const logoutUser = createAsyncThunk<
  LogoutResponse,
  void,
  { rejectValue: string }
>(
  'auth/logoutUser',
  async (_, { rejectWithValue }) => {
    try {
      const result = await window.authAPI.logout();
      return result;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Logout failed');
    }
  }
);

export const refreshToken = createAsyncThunk<
  { token: string; expiresAt: string },
  void,
  { rejectValue: string }
>(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const result = await window.authAPI.refreshToken();
      if (!result.success || !result.data) {
        return rejectWithValue(result.error || 'Token refresh failed');
      }
      return result.data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Token refresh failed');
    }
  }
);

export const checkAuthStatus = createAsyncThunk<
  { isAuthenticated: boolean; user: User | null },
  void,
  { rejectValue: string }
>(
  'auth/checkAuthStatus',
  async (_, { rejectWithValue }) => {
    try {
      const result = await window.authAPI.checkStatus();
      return result;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Auth status check failed');
    }
  }
);

export const initializeAuth = createAsyncThunk<
  { isAuthenticated: boolean; user: User | null },
  void,
  { rejectValue: string }
>(
  'auth/initializeAuth',
  async (_, { rejectWithValue }) => {
    try {
      const result = await window.authAPI.initialize();
      return result;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Auth initialization failed');
    }
  }
);

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Synchronous actions
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    updateUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
    },
    clearAuth: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.sessionId = null;
      state.expiresAt = null;
      state.error = null;
      state.lastLoginAt = null;
    },
  },
  extraReducers: (builder) => {
    // Login user
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload.success && action.payload.data) {
          state.isAuthenticated = true;
          state.user = action.payload.data.user;
          state.token = action.payload.data.token;
          state.sessionId = action.payload.data.sessionId;
          state.expiresAt = action.payload.data.expiresAt;
          state.lastLoginAt = new Date().toISOString();
          state.error = null;
        }
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Login failed';
        state.isAuthenticated = false;
      });

    // Register user
    builder
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        if (action.payload.success && action.payload.data) {
          // If registration includes authentication tokens
          if (action.payload.data.token && action.payload.data.sessionId) {
            state.isAuthenticated = true;
            state.user = action.payload.data.user;
            state.token = action.payload.data.token;
            state.sessionId = action.payload.data.sessionId;
            state.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();
            state.lastLoginAt = new Date().toISOString();
          }
          state.error = null;
        }
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Registration failed';
      });

    // Logout user
    builder
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.sessionId = null;
        state.expiresAt = null;
        state.error = null;
        state.lastLoginAt = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.isLoading = false;
        // Still clear auth data even if logout request failed
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.sessionId = null;
        state.expiresAt = null;
        state.lastLoginAt = null;
      });

    // Refresh token
    builder
      .addCase(refreshToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isLoading = false;
        state.token = action.payload.token;
        state.expiresAt = action.payload.expiresAt;
        state.error = null;
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Token refresh failed';
        // Consider logging out user if token refresh fails
        state.isAuthenticated = false;
        state.token = null;
        state.sessionId = null;
        state.expiresAt = null;
      });

    // Check auth status
    builder
      .addCase(checkAuthStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = action.payload.isAuthenticated;
        state.user = action.payload.user;
        state.error = null;
      })
      .addCase(checkAuthStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Auth status check failed';
        state.isAuthenticated = false;
        state.user = null;
      });

    // Initialize auth
    builder
      .addCase(initializeAuth.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(initializeAuth.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = action.payload.isAuthenticated;
        state.user = action.payload.user;
        state.error = null;
      })
      .addCase(initializeAuth.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Auth initialization failed';
        state.isAuthenticated = false;
        state.user = null;
      });
  },
});

// Export actions
export const { clearError, setLoading, updateUser, clearAuth } = authSlice.actions;

// Selectors
export const selectAuth = (state: { auth: AuthenticationState }) => state.auth;
export const selectIsAuthenticated = (state: { auth: AuthenticationState }) => state.auth.isAuthenticated;
export const selectUser = (state: { auth: AuthenticationState }) => state.auth.user;
export const selectAuthLoading = (state: { auth: AuthenticationState }) => state.auth.isLoading;
export const selectAuthError = (state: { auth: AuthenticationState }) => state.auth.error;

// Export reducer
export default authSlice.reducer;
