import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useTheme } from '@mui/material/styles';
import Drawer from '@mui/material/Drawer';
import Fab from '@mui/material/Fab';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';

import PerfectScrollbar from 'react-perfect-scrollbar';

import { IconSettings, IconMoon, IconSun } from '@tabler/icons-react';
import AnimateButton from '../../components/extended/AnimateButton';
import { SET_APP_THEME } from '../../store/actions';
import { gridSpacing } from '../../store/constant';


// ==============================|| LIVE CUSTOMIZATION ||============================== //

function Customization() {
  const theme = useTheme();
  const dispatch = useDispatch();
  const customization = useSelector((state: any) => state.customization);

  // state - app theme
  const [appTheme, setAppTheme] = useState(customization.appTheme)
  const handleToggleAppTheme = (event: any, newValue: string) => {
    if(newValue !== null){
      setAppTheme(newValue);
    }
  }

  useEffect(()=>{
    dispatch({type: SET_APP_THEME, appTheme});
  },[dispatch, appTheme])

  // drawer on/off
  const [open, setOpen] = useState(false);
  const handleToggle = () => {
    setOpen(!open);
  };

  return (
    <>
      {/* toggle button */}
      <Tooltip title="Live Customize">
        <Fab
          component="div"
          onClick={handleToggle}
          size="medium"
          variant="circular"
          color="secondary"
          sx={{
            borderRadius: 0,
            borderTopLeftRadius: '50%',
            borderBottomLeftRadius: '50%',
            borderTopRightRadius: '50%',
            borderBottomRightRadius: '4px',
            top: '25%',
            position: 'fixed',
            right: 10,
            zIndex: theme.zIndex.speedDial
          }}
        >
          <AnimateButton type="rotate">
            <IconButton color="inherit" size="large" disableRipple>
              <IconSettings />
            </IconButton>
          </AnimateButton>
        </Fab>
      </Tooltip>

      <Drawer
        anchor="right"
        onClose={handleToggle}
        open={open}
        PaperProps={{
          sx: {
            width: 280
          }
        }}
      >
        <PerfectScrollbar component="div">
          <Grid container spacing={gridSpacing} sx={{ p: 3 }}>
            <Grid size={12}>
              <Grid container alignItems="center" justifyContent="space-between">
                <Grid size={6}>
                  <Typography variant="subtitle1" color="inherit">
                  THEME MODE
                  </Typography>
                </Grid>
                <Grid size={6}>
                  <ToggleButtonGroup
                  // color="primary"
                  value={appTheme}
                  exclusive
                  onChange={handleToggleAppTheme}
                  aria-label="app theme"
                >
                  <ToggleButton value="light" aria-label="light theme"
                    style={{
                      color: customization.appTheme === 'light'? 'white' : 'gray',
                      backgroundColor: customization.appTheme === 'light'? theme.palette?.secondary.main :'transparent',
                    }}
                    >
                    <IconSun />
                  </ToggleButton>
                  <ToggleButton value="dark" aria-label="dark theme">
                    <IconMoon />
                  </ToggleButton>
                </ToggleButtonGroup>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </PerfectScrollbar>
      </Drawer>
    </>
  );
}

export default Customization;
