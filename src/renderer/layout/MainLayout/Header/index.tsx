import { useTheme } from '@mui/material/styles';
import {
  Box,
  IconButton,
  useMediaQuery,
  Typography,
  Stack,
} from '@mui/material';
import { IconMenu2 } from '@tabler/icons-react';

import LogoSection from '../LogoSection';
import ProfileSection from './ProfileSection';

// ==============================|| MAIN NAVBAR / HEADER ||============================== //

interface HeaderProps {
  handleLeftDrawerToggle: () => void;
}

function Header({ handleLeftDrawerToggle }: HeaderProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Stack
      direction="row"
      alignItems="center"
      justifyContent="space-between"
      sx={{ width: '100%', height: '100%' }}
    >
      {/* Left Section: Logo & Menu Toggle */}
      <Stack direction="row" alignItems="center" spacing={2}>
        <IconButton
          onClick={handleLeftDrawerToggle}
          size="large"
          edge="start"
          color="inherit"
          aria-label="toggle navigation menu"
          sx={{
            color: theme.palette.text.primary,
            '&:hover': {
              backgroundColor: theme.palette.action.hover,
            },
          }}
        >
          <IconMenu2 size="1.25rem" />
        </IconButton>

        {/* Logo - Hidden on mobile to save space */}
        {!isMobile && (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <LogoSection />
          </Box>
        )}
      </Stack>

      {/* Center Section: App Title (Mobile only) */}
      {isMobile && (
        <Typography
          variant="h6"
          component="div"
          sx={{
            flexGrow: 1,
            textAlign: 'center',
            fontWeight: 600,
            color: theme.palette.text.primary,
          }}
        >
          Facebook Post
        </Typography>
      )}

      {/* Right Section: Profile */}
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <ProfileSection />
      </Box>
    </Stack>
  );
}

export default Header;
