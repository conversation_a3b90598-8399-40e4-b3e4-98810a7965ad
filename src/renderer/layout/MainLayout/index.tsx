import { Outlet } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';

import {
  AppBar,
  Box,
  Toolbar,
  useMediaQuery,
  CssBaseline,
  useTheme,
  Container,
  Fade,
} from '@mui/material';

import Header from './Header';
import Sidebar from './Sidebar';
import { SET_MENU } from '../../store/actions';
import { drawerWidth, drawerWidthCollapsed } from '../../store/constant';

function MainLayout() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Handle sidebar state
  const leftDrawerOpened = useSelector(
    (state: any) => state.customization.opened,
  );
  const dispatch = useDispatch();

  const handleLeftDrawerToggle = () => {
    dispatch({ type: SET_MENU, opened: !leftDrawerOpened });
  };

  // Calculate main content margins based on sidebar state
  const getMainContentStyles = () => {
    const baseStyles = {
      flexGrow: 1,
      minHeight: '100vh',
      backgroundColor: theme.palette.background.default,
      transition: theme.transitions.create(['margin', 'width'], {
        easing: theme.transitions.easing.sharp,
        duration: theme.transitions.duration.leavingScreen,
      }),
    };

    if (isMobile) {
      return {
        ...baseStyles,
        width: '100%',
        marginLeft: 0,
        padding: theme.spacing(2),
      };
    }

    // For desktop: calculate margin based on expanded/collapsed state (mini drawer)
    const currentDrawerWidth = leftDrawerOpened
      ? drawerWidth
      : drawerWidthCollapsed;

    return {
      ...baseStyles,
      marginLeft: 0,
      width: `calc(100% - ${currentDrawerWidth}px)`,
      padding: theme.spacing(3),
      transition: theme.transitions.create(['margin', 'width'], {
        easing: theme.transitions.easing.easeOut,
        duration: theme.transitions.duration.enteringScreen,
      }),
    };
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <CssBaseline />

      {/* Header */}
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          backgroundColor: theme.palette.background.paper,
          borderBottom: `1px solid ${theme.palette.divider}`,
          boxShadow: theme.shadows[1],
        }}
      >
        <Toolbar sx={{ minHeight: { xs: 56, sm: 64 } }}>
          <Header handleLeftDrawerToggle={handleLeftDrawerToggle} />
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Sidebar
        drawerOpen={isMobile ? !leftDrawerOpened : leftDrawerOpened}
        drawerToggle={handleLeftDrawerToggle}
      />

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          ...getMainContentStyles(),
          marginTop: { xs: 7, sm: 8 },
          backgroundColor: theme.palette.grey[50],
        }}
      >
        <Container
          maxWidth={false}
          sx={{ px: { xs: 0, lg: 2 }, py: { xs: 1, lg: 3 } }}
        >
          <Fade in timeout={300}>
            <Box sx={{ mt: 2 }}>
              <Outlet />
            </Box>
          </Fade>
        </Container>
      </Box>
    </Box>
  );
}

export default MainLayout;
