import { Typography, Divider } from '@mui/material';
import NavGroup from './NavGroup';
import menuItem from '../../../../menu-items';

// ==============================|| SIDEBAR MENU LIST ||============================== //

interface MenuListProps {
  isMiniMode?: boolean;
}

const MenuList = ({ isMiniMode = false }: MenuListProps) => {
  const navItems = menuItem.items
    .filter((item: any) => item.type === 'group')
    .map((item: any, index: number, array: any[]) => (
      <div key={item.id}>
        <NavGroup item={item} isMiniMode={isMiniMode} />
      </div>
    ));

  return <>{navItems}</>;
};

export default MenuList;
