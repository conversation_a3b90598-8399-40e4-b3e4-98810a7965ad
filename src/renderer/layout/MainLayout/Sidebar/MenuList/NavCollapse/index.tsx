import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';

import { useTheme } from '@mui/material/styles';
import Collapse from '@mui/material/Collapse';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import Tooltip from '@mui/material/Tooltip';
import Box from '@mui/material/Box';
import Popover from '@mui/material/Popover';
import Paper from '@mui/material/Paper';

import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { IconChevronDown, IconChevronUp } from '@tabler/icons-react';
import NavItem from '../NavItem';

// ==============================|| SIDEBAR MENU LIST COLLAPSE ITEMS ||============================== //

interface NavCollapseProps {
  menu: {
    id: string;
    children?: any[];
    icon?: any;
    title: string;
    caption?: string;
    type?: string;
    url?: string; // URL để điều hướng khi click vào menu collapse
  };
  level: number;
  isMiniMode?: boolean;
}

function NavCollapse({ menu, level, isMiniMode = false }: NavCollapseProps) {
  const theme = useTheme();
  const customization = useSelector((state: any) => state.customization);
  const navigate = useNavigate();

  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const { pathname } = useLocation();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (isMiniMode) {
      // In mini mode, handle popover
      if (anchorEl) {
        setAnchorEl(null);
      } else {
        setAnchorEl(event.currentTarget);
      }
    } else {
      // In normal mode, handle inline expansion
      setOpen(!open);
      setSelected(menu.id);

      if (menu.url) {
        navigate(menu.url);
      } else if (menu.children) {
        // If there are children, expand/collapse
        setOpen(!open);
      }
    }
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  // Close popover when switching to normal mode or component unmounts
  useEffect(() => {
    if (!isMiniMode && anchorEl) {
      setAnchorEl(null);
    }
  }, [isMiniMode, anchorEl]);

  // Auto-expand and select menu based on current path
  useEffect(() => {
    let shouldBeOpen = false;
    let shouldBeSelected = false;

    // Helper function to check if current path belongs to this menu hierarchy
    const isPathInMenuHierarchy = () => {
      // Check if current path matches menu's direct URL exactly
      if (menu.url === pathname) {
        return true;
      }

      // Check if current path starts with menu's URL prefix (for sub-routes)
      if (menu.url && pathname.startsWith(menu.url + '/')) {
        return true;
      }

      // Check if current path matches any child URL exactly
      if (menu.children) {
        for (const item of menu.children) {
          if (item.url === pathname) {
            return true;
          }

          // Check if current path starts with child URL prefix
          if (item.url && pathname.startsWith(item.url + '/')) {
            return true;
          }

          // Check nested children
          if (item.children?.length) {
            for (const child of item.children) {
              if (child.url === pathname) {
                return true;
              }

              // Check if current path starts with nested child URL prefix
              if (child.url && pathname.startsWith(child.url + '/')) {
                return true;
              }
            }
          }
        }
      }

      return false;
    };

    // Determine if menu should be open and selected
    if (isPathInMenuHierarchy()) {
      shouldBeOpen = true;

      // Only mark as selected if we're on the exact parent URL or a direct child
      if (menu.url === pathname) {
        shouldBeSelected = true;
      } else if (menu.children) {
        // Check if we're on a direct child URL
        const isOnDirectChild = menu.children.some((item) => {
          if (item.url === pathname) return true;
          if (item.children?.length) {
            return item.children.some((child: any) => child.url === pathname);
          }
          return false;
        });

        if (isOnDirectChild) {
          shouldBeSelected = true;
        }
      }
    }

    // Only update state if it's actually different to prevent unnecessary re-renders
    if (open !== shouldBeOpen) {
      setOpen(shouldBeOpen);
    }
    if ((selected === menu.id) !== shouldBeSelected) {
      setSelected(shouldBeSelected ? menu.id : null);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, menu.children, menu.url, menu.id]);

  // menu collapse & item
  const menus = menu.children?.map((item) => {
    switch (item.type) {
      case 'collapse':
        return (
          <NavCollapse
            key={item.id}
            menu={item}
            level={level + 1}
            isMiniMode={false} // Force normal mode for nested items in popover
          />
        );
      case 'item':
        return (
          <NavItem
            key={item.id}
            item={item}
            level={level + 1}
            isMiniMode={false} // Force normal mode for nested items in popover
          />
        );
      default:
        return (
          <Typography key={item.id} variant="h6" color="error" align="center">
            Menu Items Error
          </Typography>
        );
    }
  });

  const Icon = menu.icon;
  const menuIcon = menu.icon ? (
    <Icon
      strokeWidth={1.5}
      size="1.3rem"
      style={{ marginTop: 'auto', marginBottom: 'auto' }}
    />
  ) : (
    <FiberManualRecordIcon
      sx={{
        width: selected === menu.id ? 8 : 6,
        height: selected === menu.id ? 8 : 6,
      }}
      fontSize={level > 0 ? 'inherit' : 'medium'}
    />
  );

  // In mini mode, show only parent icon with tooltip and right-side popover for children
  if (isMiniMode) {
    const popoverOpen = Boolean(anchorEl);

    return (
      <>
        <Tooltip title={menu.title} placement="right" arrow>
          <ListItemButton
            sx={{
              borderRadius: 2,
              mb: 0.5,
              minHeight: 48,
              justifyContent: 'center',
              px: 2.5,
            }}
            selected={selected === menu.id}
            onClick={handleClick}
            aria-describedby={popoverOpen ? `popover-${menu.id}` : undefined}
            aria-haspopup="true"
            aria-expanded={popoverOpen ? 'true' : 'false'}
          >
            <ListItemIcon sx={{ minWidth: 0, justifyContent: 'center' }}>
              {menuIcon}
            </ListItemIcon>
          </ListItemButton>
        </Tooltip>

        {/* Right-side popover for children in mini mode */}
        <Popover
          id={`popover-${menu.id}`}
          open={popoverOpen}
          anchorEl={anchorEl}
          onClose={handlePopoverClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          sx={{
            '& .MuiPopover-paper': {
              ml: 1, // Add margin to separate from sidebar
              minWidth: 200,
              maxWidth: 280,
              borderRadius: 2,
              boxShadow: theme.shadows[8],
              border: `1px solid ${theme.palette.divider}`,
            },
          }}
        >
          <Paper sx={{ p: 1 }}>
            <Typography
              variant="subtitle2"
              sx={{
                px: 2,
                py: 1,
                fontWeight: 600,
                color: theme.palette.text.primary,
                borderBottom: `1px solid ${theme.palette.divider}`,
                mb: 1,
              }}
            >
              {menu.title}
            </Typography>
            <Box onClick={handlePopoverClose}>{menus}</Box>
          </Paper>
        </Popover>
      </>
    );
  }

  // Normal expanded state
  return (
    <>
      <ListItemButton
        sx={{
          borderRadius: 2,
          alignItems: 'flex-start',
          backgroundColor: level > 1 ? 'transparent !important' : 'inherit',
          py: 0.5,
          pl: `${level * 24}px`,
          mb: 0.5,
          ':hover': {
            backgroundColor: theme.palette.action.hover,
          },
        }}
        selected={selected === menu.id}
        onClick={handleClick}
      >
        <ListItemIcon sx={{ my: 'auto', minWidth: !menu.icon ? 18 : 36 }}>
          {menuIcon}
        </ListItemIcon>
        <ListItemText
          primary={
            <Typography
              variant={selected === menu.id ? 'h5' : 'body1'}
              color="inherit"
              sx={{ my: 'auto' }}
            >
              {menu.title}
            </Typography>
          }
          secondary={
            menu.caption && (
              <Typography
                variant="caption"
                sx={{ ...theme.typography.subMenuCaption }}
                display="block"
                gutterBottom
              >
                {menu.caption}
              </Typography>
            )
          }
        />
        {open ? (
          <IconChevronUp
            stroke={1.5}
            size="1rem"
            style={{ marginTop: 'auto', marginBottom: 'auto' }}
          />
        ) : (
          <IconChevronDown
            stroke={1.5}
            size="1rem"
            style={{ marginTop: 'auto', marginBottom: 'auto' }}
          />
        )}
      </ListItemButton>
      <Collapse in={open} timeout="auto" unmountOnExit>
        <List
          component="div"
          disablePadding
          sx={{
            position: 'relative',
            '&:after': {
              content: "''",
              position: 'absolute',
              left: '32px',
              top: 0,
              height: '100%',
              width: '1px',
              opacity: 1,
              background: theme.palette.primary.light,
            },
          }}
        >
          {menus}
        </List>
      </Collapse>
    </>
  );
}

export default NavCollapse;
