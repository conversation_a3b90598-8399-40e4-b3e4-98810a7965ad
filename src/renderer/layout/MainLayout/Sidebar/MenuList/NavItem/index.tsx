/* eslint-disable react/require-default-props */
import React, { forwardRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';

import { useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';

import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { MENU_OPEN } from '../../../../../store/actions';
import { Tooltip } from '@mui/material';

// ==============================|| SIDEBAR MENU LIST ITEMS ||============================== //

interface NavItemProps {
  item: {
    id: string;
    title: string;
    caption?: string;
    icon?: any;
    url?: string;
    target?: boolean;
    external?: boolean;
    disabled?: boolean;
    chip?: {
      color: string;
      variant: string;
      size: string;
      label: string;
      avatar?: any;
    };
  };
  level: number;
  isMiniMode?: boolean;
}

const NavItemLink = forwardRef<
  HTMLAnchorElement,
  Omit<React.ComponentProps<typeof Link>, 'to'> & { to: string } & {
    target?: string;
  }
>(({ to, ...props }, ref) => <Link ref={ref} to={to} {...props} />);

function NavItem({ item, level, isMiniMode = false }: NavItemProps) {
  const theme = useTheme();
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const customization = useSelector((state: any) => state.customization);

  const Icon = item.icon;
  const itemIcon = item?.icon ? (
    <Icon stroke={1.5} size="1.3rem" />
  ) : (
    <FiberManualRecordIcon
      sx={{
        width:
          customization.isOpen.findIndex((id: any) => id === item?.id) > -1
            ? 8
            : 6,
        height:
          customization.isOpen.findIndex((id: any) => id === item?.id) > -1
            ? 8
            : 6,
      }}
      fontSize={level > 0 ? 'inherit' : 'medium'}
    />
  );

  let itemTarget = '_self';
  if (item.target) {
    itemTarget = '_blank';
  }

  let listItemProps: any = {};
  if (item?.external) {
    listItemProps = {
      component: 'a',
      href: item.url ?? '',
      target: itemTarget,
    };
  } else if (item.url) {
    listItemProps = {
      component: NavItemLink,
      to: item.url,
      target: itemTarget,
    };
  }

  const itemHandler = (id: any) => {
    dispatch({ type: MENU_OPEN, id });
    // if (matchesSM) dispatch({ type: SET_MENU, opened: false });
  };

  // active menu item on page load
  useEffect(() => {
    const currentIndex = document.location.pathname
      .toString()
      .split('/')
      .findIndex((id) => id === item.id);
    if (currentIndex > -1) {
      dispatch({ type: MENU_OPEN, id: item.id });
    }
    // eslint-disable-next-line
  }, [pathname]);

  // In mini mode, show only icon with tooltip
  if (isMiniMode) {
    return (
      <Tooltip title={item.title} placement="right" arrow>
        <ListItemButton
          {...listItemProps}
          disabled={item.disabled}
          sx={{
            borderRadius: `${customization.borderRadius}px`,
            mb: 0.5,
            minHeight: 48,
            justifyContent: 'center',
            px: 2.5,
          }}
          selected={
            customization.isOpen.findIndex((id: any) => id === item.id) > -1
          }
          onClick={() => itemHandler(item.id)}
        >
          <ListItemIcon sx={{ minWidth: 0, justifyContent: 'center' }}>
            {itemIcon}
          </ListItemIcon>
        </ListItemButton>
      </Tooltip>
    );
  }

  // Normal expanded state
  return (
    <ListItemButton
      {...listItemProps}
      disabled={item.disabled}
      sx={{
        borderRadius: 2,
        alignItems: 'flex-start',
        backgroundColor: level > 1 ? 'transparent !important' : 'inherit',
        py: 0.5,
        pl: `${level * 24}px`,
        mb: 0.5,
        ':hover': {
          backgroundColor: theme.palette.action.hover,
        },
      }}
      selected={
        customization.isOpen.findIndex((id: any) => id === item.id) > -1 &&
        !isMiniMode
      }
      onClick={() => itemHandler(item.id)}
    >
      <ListItemIcon sx={{ my: 'auto', minWidth: !item?.icon ? 18 : 36 }}>
        {itemIcon}
      </ListItemIcon>
      <ListItemText
        primary={
          <Typography
            variant={
              customization.isOpen.findIndex((id: any) => id === item.id) >
                -1 && !isMiniMode
                ? 'h5'
                : 'body1'
            }
            color="inherit"
          >
            {item.title}
          </Typography>
        }
        secondary={
          item.caption && (
            <Typography
              variant="caption"
              sx={{ ...theme.typography.subMenuCaption }}
              display="block"
              gutterBottom
            >
              {item.caption}
            </Typography>
          )
        }
      />
      {item.chip && (
        <Chip
          color={
            item.chip.color as
              | 'default'
              | 'primary'
              | 'secondary'
              | 'error'
              | 'info'
              | 'success'
              | 'warning'
              | undefined
          }
          variant={item.chip.variant as 'filled' | 'outlined' | undefined}
          size={item.chip.size as 'small' | 'medium' | undefined}
          label={item.chip.label}
          avatar={item.chip.avatar && <Avatar>{item.chip.avatar}</Avatar>}
        />
      )}
    </ListItemButton>
  );
}

export default NavItem;
