import { useTheme, alpha } from '@mui/material';
import {
  Box,
  Drawer,
  Stack,
  useMediaQuery,
  Typography,
  Divider,
  styled,
} from '@mui/material';

import PerfectScrollbar from 'react-perfect-scrollbar';

import MenuList from './MenuList';
import LogoSection from '../LogoSection';
import { drawerWidth, drawerWidthCollapsed } from '../../../store/constant';
import ZaloIcon from '../../../assets/icons/zaloIcon';

// ==============================|| STYLED COMPONENTS ||============================== //

const openedMixin = (theme: any) => ({
  width: drawerWidth,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: 'hidden' as const,
});

const closedMixin = (theme: any) => ({
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: 'hidden' as const,
  width: drawerWidthCollapsed,
});

interface MiniDrawerProps {
  open?: boolean;
}

const MiniDrawer = styled(Drawer, {
  shouldForwardProp: (prop) => prop !== 'open',
})<MiniDrawerProps>(({ theme, open }) => ({
  width: drawerWidth,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  boxSizing: 'border-box',
  ...(open && {
    ...openedMixin(theme),
    '& .MuiDrawer-paper': openedMixin(theme),
  }),
  ...(!open && {
    ...closedMixin(theme),
    '& .MuiDrawer-paper': closedMixin(theme),
  }),
}));

// ==============================|| SIDEBAR DRAWER ||============================== //

interface SidebarProps {
  drawerOpen: boolean;
  drawerToggle: (event: {}, reason: 'backdropClick' | 'escapeKeyDown') => void;
  window?: Window;
}

const Sidebar = ({ drawerOpen, drawerToggle, window }: SidebarProps) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // For desktop: use mini drawer, for mobile: use temporary drawer
  const isMiniMode = !drawerOpen;

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Mobile Logo */}
      {isMobile && (
        <Box
          sx={{
            p: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <LogoSection />
        </Box>
      )}

      {/* Navigation Menu */}
      <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
        <PerfectScrollbar
          component="div"
          style={{
            height: '100%',
            padding: theme.spacing(1),
            overflow: 'hidden',
          }}
        >
          <MenuList isMiniMode={isMiniMode} />
        </PerfectScrollbar>
      </Box>

      {/* Thông tin liên hệ */}
      <Box sx={isMiniMode ? { p: 2, mt: 2, mx: 2 } : { mx: 2 }}>
        <Stack
          direction={`${isMiniMode ? 'column' : 'row'}`}
          spacing={2}
          justifyContent="center"
          alignItems="center"
          sx={{ mb: 1 }}
        >
          <Box
            component="a"
            href="https://zalo.me/0123456789"
            target="_blank"
            rel="noopener noreferrer"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.1)',
              },
            }}
          >
            <ZaloIcon sx={{ width: 40, height: 40 }} />
          </Box>

          <Box
            component="a"
            href="https://your-website.com"
            target="_blank"
            rel="noopener noreferrer"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 32,
              height: 32,
              borderRadius: '50%',
              backgroundColor: '#4CAF50',
              textDecoration: 'none',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.1)',
                boxShadow: '0 4px 8px rgba(76, 175, 80, 0.3)',
              },
            }}
          >
            <Typography sx={{ fontSize: '16px' }}>🌐</Typography>
          </Box>

          <Box
            component="a"
            href="mailto:<EMAIL>"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 32,
              height: 32,
              borderRadius: '50%',
              backgroundColor: '#FF5722',
              textDecoration: 'none',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.1)',
                boxShadow: '0 4px 8px rgba(255, 87, 34, 0.3)',
              },
            }}
          >
            <Typography sx={{ fontSize: '16px' }}>📧</Typography>
          </Box>

          <Box
            component="a"
            href="https://m.me/your-page"
            target="_blank"
            rel="noopener noreferrer"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 32,
              height: 32,
              borderRadius: '50%',
              background: 'linear-gradient(45deg, #0084FF, #00C6FF)',
              textDecoration: 'none',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'scale(1.1)',
                boxShadow: '0 4px 8px rgba(0, 132, 255, 0.3)',
              },
            }}
          >
            <Typography sx={{ fontSize: '16px' }}>💬</Typography>
          </Box>
        </Stack>
      </Box>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
        <Stack direction="row" justifyContent="center">
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              px: 1,
              py: 0.5,
              borderRadius: 1,
              backgroundColor: theme.palette.action.hover,
            }}
          >
            v1.0.0
          </Typography>
        </Stack>
      </Box>
    </Box>
  );

  const container =
    window !== undefined ? () => window.document.body : undefined;

  if (isMobile) {
    // Mobile: Use temporary drawer
    return (
      <Drawer
        container={container}
        variant="temporary"
        anchor="left"
        open={drawerOpen}
        onClose={drawerToggle}
        sx={{
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            backgroundColor: theme.palette.background.paper,
            color: theme.palette.text.primary,
            borderRight: `1px solid ${theme.palette.divider}`,
            boxShadow: theme.shadows[8],
            top: `12%`,
            height: 'calc(100vh - 12%)',
          },
        }}
        ModalProps={{
          keepMounted: true,
          disableScrollLock: true,
        }}
      >
        {drawer}
      </Drawer>
    );
  }

  // Desktop: Use mini drawer
  return (
    <MiniDrawer
      variant="permanent"
      open={drawerOpen}
      sx={{
        '& .MuiDrawer-paper': {
          backgroundColor: theme.palette.background.paper,
          color: theme.palette.text.primary,
          borderRight: `1px solid ${theme.palette.divider}`,
          top: 88, // AppBar height
          height: 'calc(100vh - 88px)',
        },
      }}
    >
      {drawer}
    </MiniDrawer>
  );
};

export default Sidebar;
