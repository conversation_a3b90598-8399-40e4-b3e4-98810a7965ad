import React, { useCallback, useMemo, useState } from "react";

export interface DataItem {
  id: string;
  [key: string]: any;
}

export interface GenericTableProps<T> {
  data: T[];
  total: number;
}

export interface Column<T> {
  key: keyof T | string;
  text: string;
  sortable?: boolean;
  align?: 'left' | 'right' | 'center';
  width?: number | string;
  minWidth?: number | string;
  render?: (value: any, item: T) => React.ReactNode;
}

export interface Action<T> {
  key: string;
  label: string;
  icon: React.ReactNode;
  size?: "small" | "medium" | "large";
  color?: "inherit" | "primary" | "default" | "secondary" | "error" | "info" | "success" | "warning";
  onClick: (item: T) => void;
  disabled?: (item: T) => boolean;
  hidden?: (item: T) => boolean;
}

export type RowProps<T> = {
  item: T;
  columns: Column<T>[];
  actions: Action<T>[];
  selected?: boolean;
  onSelect: (selectedId: string) => void;
}

export const visuallyHidden = {
  border: 0,
  margin: -1,
  padding: 0,
  width: '1px',
  height: '1px',
  overflow: 'hidden',
  position: 'absolute',
  whiteSpace: 'nowrap',
  clip: 'rect(0 0 0 0)',
} as const;

export function emptyRows(page: number, rowsPerPage: number, arrayLength: number) {
  return page ? Math.max(0, (1 + page) * rowsPerPage - arrayLength) : 0;
}

/**
 * Generic base table with sort, select, paginate
 * @param data
 * @returns 
 */
export function useTable<T extends DataItem>({ data, total }: GenericTableProps<T>) {
  const [page, setPage] = useState(0);
  const [orderBy, setOrderBy] = useState<string>('id');
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [selected, setSelected] = useState<string[]>([]);
  const [order, setOrder] = useState<'asc' | 'desc'>('asc');


  const onSort = useCallback((columnKey: string) => {
    setOrderBy(columnKey);
    setOrder((prevOrder) => (prevOrder === 'asc' && orderBy === columnKey ? 'desc' : 'asc'));
  }, [orderBy]);

  const sortedData = useMemo(() => {
    if (!orderBy) return data;

    return [...data].sort((a, b) => {
      const valueA = a[orderBy];
      const valueB = b[orderBy];

      if (valueA == null || valueB == null) return 0;

      // Xử lý thời gian
      if (valueA instanceof Date && valueB instanceof Date) {
        return order === 'asc'
          ? valueA.getTime() - valueB.getTime()
          : valueB.getTime() - valueA.getTime();
      }

      // Xử lý chuỗi thời gian
      const dateA = Date.parse(valueA);
      const dateB = Date.parse(valueB);
      if (!Number.isNaN(dateA) && !Number.isNaN(dateB)) {
        return order === 'asc' ? dateA - dateB : dateB - dateA;
      }

      // Xử lý chuỗi
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return order === 'asc'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      }

      // Xử lý số
      if (typeof valueA === 'number' && typeof valueB === 'number') {
        return order === 'asc' ? valueA - valueB : valueB - valueA;
      }

      return 0;
    });
  }, [data, order, orderBy]);

  const paginatedData = useMemo(() => {
    const start = page * rowsPerPage;
    const end = start + rowsPerPage;
    return sortedData.slice(start, end);
  }, [sortedData, page, rowsPerPage]);

  const onSelectAllRows = useCallback(
    (checked: boolean, newSelecteds: string[]) => {
      const newSelected = checked ? newSelecteds : [];
      setSelected(newSelected);
    },
    []
  );

  const onSelectRow = useCallback(
    (value: string) => {
      const newSelected = selected.includes(value)
        ? selected.filter((v) => v !== value)
        : [...selected, value];
      setSelected(newSelected);
    },
    [selected]
  );

  const onResetPage = useCallback(() => setPage(0), []);

  const onChangePage = useCallback((_event: unknown, newPage: number) => {
    setPage(newPage);
  }, []);

  const onChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  }, []);

  return {
    page,
    order,
    orderBy,
    rowsPerPage,
    selected,
    sortedData: paginatedData,
    totalRows: total,
    onSort,
    onSelectAllRows,
    onSelectRow,
    onResetPage,
    onChangePage,
    onChangeRowsPerPage,
  };
}