import TableRow from '@mui/material/TableRow';
import Checkbox from '@mui/material/Checkbox';
import TableHead from '@mui/material/TableHead';
import TableCell from '@mui/material/TableCell';
import TableSortLabel from '@mui/material/TableSortLabel';
import { Box } from '@mui/material';

import React from 'react';
import { DataItem, Column, visuallyHidden } from './TableConfig';

type TableHeadProps<T> = {
  order: 'asc' | 'desc';
  orderBy: string;
  rowCount: number;
  numSelected: number;
  columns: Column<T>[];
  onSort: (key: string) => void;
  onSelectAllRows: (checked: boolean, ids: string[]) => void;
};

export default function TableHeadCustom<T extends DataItem>({
  order = 'desc',
  orderBy,
  rowCount,
  numSelected,
  columns,
  onSort,
  onSelectAllRows,
}: TableHeadProps<T>) {
  return (
    <TableHead>
      <TableRow>
        <TableCell padding="checkbox">
          <Checkbox
            indeterminate={numSelected > 0 && numSelected < rowCount}
            checked={rowCount > 0 && numSelected === rowCount}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
              onSelectAllRows(event.target.checked, [])
            }
          />
        </TableCell>

        {columns.map((headCell) => (
          <TableCell
            key={headCell.key as string}
            align={headCell.align || 'left'}
            sx={{ width: headCell.width, minWidth: headCell.minWidth }}
          >
            {headCell.sortable ? (
              <TableSortLabel
                hideSortIcon
                active={orderBy === headCell.key}
                direction={orderBy === headCell.key ? order : 'asc'}
                onClick={() => onSort(headCell.key as string)}
              >
                {headCell.text}
                {orderBy === headCell.key ? (
                  <Box sx={{ ...visuallyHidden }}>
                    {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                  </Box>
                ) : null}
              </TableSortLabel>
            ) : (
              headCell.text
            )}
          </TableCell>
        ))}

        <TableCell>Hành động</TableCell>
      </TableRow>
    </TableHead>
  );
}
