import Tooltip from '@mui/material/Tooltip';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputAdornment from '@mui/material/InputAdornment';
import DeleteIcon from '@mui/icons-material/Delete';
import FilterAltIcon from '@mui/icons-material/FilterAlt';
import { IconSearch } from '@tabler/icons-react';
import React from 'react';


type TableToolbarProps = {
  numSelected: number;
  searchValue: string;
  onSearch: (event: React.ChangeEvent<HTMLInputElement>) => void;
};

export default function TableToolbar({ numSelected, searchValue, onSearch }: TableToolbarProps) {
  return (
    <Toolbar
      sx={{
        height: 96,
        display: 'flex',
        justifyContent: 'space-between',
        p: (theme) => theme.spacing(0, 1, 0, 3),
        ...(numSelected > 0 && {
          color: 'primary.main',
          bgcolor: 'primary.lighter',
        }),
      }}
    >
      {numSelected > 0 ? (
        <Typography component="div" variant="subtitle1">
          {numSelected} selected
        </Typography>
      ) : (
        <OutlinedInput
          fullWidth
          value={searchValue}
          onChange={onSearch}
          placeholder="Nhập từ cần tìm kiếm..."
          startAdornment={
            <InputAdornment position="start">
              <IconSearch stroke={1.5} size="1rem" color='inherit' />
            </InputAdornment>
          }
          sx={{ maxWidth: 320 }}
        />
      )}

      {numSelected > 0 ? (
        <Tooltip title="Delete">
          <IconButton>
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Filter list">
          <IconButton>
            <FilterAltIcon />
          </IconButton>
        </Tooltip>
      )}
    </Toolbar>
  );
}
