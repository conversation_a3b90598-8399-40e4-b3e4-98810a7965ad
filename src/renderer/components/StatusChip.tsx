import React from 'react';
import { Chip, useTheme } from '@mui/material';
import { getStatusConfig } from '../../utils/campaignStatus';
import { getAccountStatusConfig } from '../../utils/accountStatus';

interface StatusChipProps {
  status: string;
  variant?: 'filled' | 'outlined';
  size?: 'small' | 'medium';
  type?: 'campaign' | 'account';
  sx?: object;
}

/**
 * Reusable status chip component that displays status with appropriate colors
 * @param status - Status value to display
 * @param variant - Chip variant (filled or outlined)
 * @param size - Chip size
 * @param type - Type of status (campaign or account)
 * @param sx - Additional styles
 */
const StatusChip: React.FC<StatusChipProps> = ({
  status,
  variant = 'filled',
  size = 'small',
  type = 'campaign',
  sx = {},
}) => {
  const theme = useTheme();
  const config =
    type === 'account'
      ? getAccountStatusConfig(status, theme)
      : getStatusConfig(status, theme);

  return (
    <Chip
      label={config.label}
      variant={variant}
      size={size}
      sx={{
        fontWeight: 600,
        borderRadius: 2,
        fontSize: '0.75rem',
        minWidth: 100,
        backgroundColor: config.bgColor,
        color: config.textColor,
        border: `1px solid ${config.borderColor}`,
        ...sx,
      }}
    />
  );
};

export default StatusChip;
