import { useTheme } from '@mui/material';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

export interface ConfirmModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  title: string;
  content: string;
  actions: {
    cancel: string,
    submit: string,
  }
}

export default function ConfirmModal({ open, onClose, onSubmit, title, content, actions }: ConfirmModalProps) {
  const theme = useTheme();

  const handleSubmit = () => {
    onSubmit();
    onClose();
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <Box m={2}>
        <DialogTitle sx={{fontFamily: theme.typography.h3}}>{title}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description" paddingY={2}>
            {content}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} variant="outlined">
            {actions.cancel}
          </Button>
          <Button onClick={handleSubmit} type="submit" variant="contained">
            {actions.submit}
          </Button>
        </DialogActions>
        
      </Box>
    </Dialog>
  );
};