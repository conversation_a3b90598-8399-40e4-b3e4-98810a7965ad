import React from 'react';
import {
  Box,
  Paper,
  Alert,
  Button,
  useTheme,
  alpha,
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

interface ErrorStateProps {
  error: string;
  onBack: () => void;
  severity?: 'error' | 'warning' | 'info';
}

/**
 * Reusable error state component
 */
const ErrorState: React.FC<ErrorStateProps> = ({
  error,
  onBack,
  severity = 'error',
}) => {
  const theme = useTheme();
  const severityColor = severity === 'error' 
    ? theme.palette.error.main || '#f44336'
    : severity === 'warning'
    ? theme.palette.warning.main || '#ff9800'
    : theme.palette.info.main || '#2196f3';

  return (
    <Box sx={{ p: 3 }}>
      <Paper
        elevation={1}
        sx={{
          p: 4,
          borderRadius: 3,
          textAlign: 'center',
          border: `1px solid ${alpha(severityColor, 0.2)}`,
        }}
      >
        <Alert
          severity={severity}
          sx={{
            mb: 3,
            borderRadius: 2,
            '& .MuiAlert-message': {
              fontSize: '1rem',
            },
          }}
        >
          {error}
        </Alert>
        <Button
          variant="contained"
          startIcon={<ArrowBackIcon />}
          onClick={onBack}
          sx={{
            borderRadius: 2,
            px: 3,
            py: 1,
          }}
        >
          Quay lại danh sách
        </Button>
      </Paper>
    </Box>
  );
};

export default ErrorState;
