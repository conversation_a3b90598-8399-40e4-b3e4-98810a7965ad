import { useTheme } from '@mui/material/styles';
import logoTop from 'assets/images/logoTop.png';

/**
 * if you want to use image instead of <svg> uncomment following.
 *
 * import logoDark from 'assets/images/logo-dark.svg';
 * import logo from 'assets/images/logo.svg';
 *
 */

function Logo() {
  const theme = useTheme();

  return (
    /**
     * if you want to use image instead of svg uncomment following, and comment out <svg> element.
     *
     * <img src={logo} alt="Berry" width="100" />
     *
     */
    <img
      src={theme.palette.mode === 'dark' ? logoTop : logoTop}
      alt="Berry"
      width="88"
    />
  );
}

export default Logo;
