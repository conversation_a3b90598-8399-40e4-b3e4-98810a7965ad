/**
 * Environment Settings Component
 * Allows switching between environments during development
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Typography,
  Box,
  Chip,
  Alert,
  Divider,
} from '@mui/material';
import { Environment } from '../../../config/ApiConfig';
import EnvironmentManager from '../../../config/environment';

interface EnvironmentSettingsProps {
  open: boolean;
  onClose: () => void;
}

const EnvironmentSettings: React.FC<EnvironmentSettingsProps> = ({ open, onClose }) => {
  const [selectedEnvironment, setSelectedEnvironment] = useState<Environment>('prod');
  const [currentConfig, setCurrentConfig] = useState(EnvironmentManager.config);

  useEffect(() => {
    if (open) {
      setSelectedEnvironment(EnvironmentManager.currentEnvironment);
      setCurrentConfig(EnvironmentManager.config);
    }
  }, [open]);

  const handleEnvironmentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedEnvironment(event.target.value as Environment);
  };

  const handleApply = async () => {
    try {
      // Switch environment locally
      EnvironmentManager.switchEnvironment(selectedEnvironment);
      
      // Update API configuration through IPC
      const result = await window.authAPI.setEnvironment(selectedEnvironment);
      
      if (result.success) {
        setCurrentConfig(EnvironmentManager.config);
        onClose();
        
        // Optionally reload the app to apply changes
        if (window.confirm('Environment changed. Reload the application to apply all changes?')) {
          window.location.reload();
        }
      } else {
        console.error('Failed to set environment:', result.error);
      }
    } catch (error) {
      console.error('Error switching environment:', error);
    }
  };

  const environments = EnvironmentManager.getAvailableEnvironments();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Environment Settings</DialogTitle>
      <DialogContent>
        <Box mb={2}>
          <Alert severity="info">
            Switch between different API environments. This setting is persisted locally.
          </Alert>
        </Box>

        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            Current Environment
          </Typography>
          <Box display="flex" alignItems="center" gap={1}>
            <Chip 
              label={currentConfig.displayName} 
              color={currentConfig.isProduction ? 'error' : 'primary'}
              variant="filled"
            />
            <Typography variant="body2" color="textSecondary">
              {currentConfig.apiHost}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <FormControl component="fieldset">
          <FormLabel component="legend">Select Environment</FormLabel>
          <RadioGroup
            value={selectedEnvironment}
            onChange={handleEnvironmentChange}
          >
            {environments.map((env) => (
              <Box key={env.name} mb={1}>
                <FormControlLabel
                  value={env.name}
                  control={<Radio />}
                  label={
                    <Box>
                      <Typography variant="body1" fontWeight="medium">
                        {env.displayName}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        API: {env.apiHost}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        CRM: {env.crmApiHost}
                      </Typography>
                      <Box mt={0.5}>
                        {env.isDevelopment && (
                          <Chip size="small" label="Development" color="info" />
                        )}
                        {env.isProduction && (
                          <Chip size="small" label="Production" color="error" />
                        )}
                        {!env.isDevelopment && !env.isProduction && (
                          <Chip size="small" label="Testing" color="warning" />
                        )}
                      </Box>
                    </Box>
                  }
                />
              </Box>
            ))}
          </RadioGroup>
        </FormControl>

        {selectedEnvironment !== EnvironmentManager.currentEnvironment && (
          <Box mt={2}>
            <Alert severity="warning">
              Changing the environment will affect all API calls. Make sure you have the correct credentials for the selected environment.
            </Alert>
          </Box>
        )}

        {/* Development info */}
        {EnvironmentManager.isDevelopment() && (
          <Box mt={3}>
            <Typography variant="h6" gutterBottom>
              Debug Information
            </Typography>
            <Box component="pre" sx={{ 
              fontSize: '0.75rem', 
              backgroundColor: '#f5f5f5', 
              p: 1, 
              borderRadius: 1,
              overflow: 'auto',
              maxHeight: 200
            }}>
              {JSON.stringify(EnvironmentManager.getEnvironmentInfo(), null, 2)}
            </Box>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={handleApply} 
          variant="contained"
          disabled={selectedEnvironment === EnvironmentManager.currentEnvironment}
        >
          Apply Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EnvironmentSettings;
