import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import { FormSelectProps, selectOptions } from "./formType";


function FormSelect ({
  label,
  name,
  value,
  options,
  ...rest
}: FormSelectProps) {
  return (
    <FormControl fullWidth sx={{ marginTop: 1}}>
      <InputLabel>{label}</InputLabel>
      <Select
        name={name}
        value={value}
        label={label}
        {...rest}
      >
        {options.map((item: selectOptions) => <MenuItem value={item.value}>{item.text}</MenuItem>)}
      </Select>
    </FormControl>
  );
};

FormSelect.defaultProps = {
  id: undefined,
};

export default FormSelect;