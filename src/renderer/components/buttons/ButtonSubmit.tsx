import CircularProgress from "@mui/material/CircularProgress";
import Button, { ButtonProps } from "@mui/material/Button";
import { ReactNode } from "react";


interface BtnProps extends ButtonProps {
  children: ReactNode;
  isSubmitting: boolean;
}

export default function ButtonSubmit(props: BtnProps){
  const { isSubmitting, children, ...otherProps } = props;

  const configuration = {
    ...otherProps,
    fullWidth: true
  };

  return (
    <Button
      {...configuration}
      endIcon={isSubmitting && <CircularProgress color="inherit" size={20} />}
      disabled={isSubmitting}
    >
      {children}
    </Button>
  );
};
