import { styled } from '@mui/material/styles';
import Button from '@mui/material/Button';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { ChangeEvent, InputHTMLAttributes } from 'react';

interface InputFileUploadProps extends InputHTMLAttributes<HTMLElement> {
  title: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
}

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

export default function InputFileUpload({title, onChange}: InputFileUploadProps) {
  const handleUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onChange(e);
    }
    e.target.value = ''; // Reset the input value after upload
  };
  return (
    <Button
      component="label"
      role={undefined}
      variant="contained"
      tabIndex={-1}
      startIcon={<CloudUploadIcon />}
    >
      {title}
      <VisuallyHiddenInput
        type="file"
        onChange={handleUpload}
        multiple
      />
    </Button>
  );
}