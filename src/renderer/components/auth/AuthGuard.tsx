/**
 * Authentication Guard Component (Simplified)
 * Protects routes that require authentication using direct auth service
 */

import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import directAuthService from '../../services/DirectAuthService';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children, fallback }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      try {
        const { isAuthenticated } = directAuthService.initializeAuth();
        setIsAuthenticated(isAuthenticated);

        if (!isAuthenticated) {
          // Store the attempted location for redirect after login
          const from = location.pathname + location.search;
          navigate('/pages/login/login3', {
            replace: true,
            state: { from }
          });
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
        navigate('/pages/login/login3', { replace: true });
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [navigate, location]);

  // Show loading spinner while checking auth
  if (isLoading) {
    return (
      fallback || (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          gap={2}
        >
          <CircularProgress size={40} />
          <Typography variant="body2" color="textSecondary">
            Checking authentication...
          </Typography>
        </Box>
      )
    );
  }

  // If not authenticated, return null (navigation will handle redirect)
  if (!isAuthenticated) {
    return null;
  }

  // If authenticated, render children
  return <>{children}</>;
};

export default AuthGuard;
