/**
 * Authentication Guard Component
 * Protects routes that require authentication
 */

import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Box, CircularProgress, Typography } from '@mui/material';
import { selectIsAuthenticated, selectAuthLoading, initializeAuth } from '../../store/slices/authSlice';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children, fallback }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const isLoading = useSelector(selectAuthLoading);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initAuth = async () => {
      try {
        // Initialize authentication on app startup
        await dispatch(initializeAuth() as any);
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    if (!isInitialized) {
      initAuth();
    }
  }, [dispatch, isInitialized]);

  useEffect(() => {
    if (isInitialized && !isAuthenticated && !isLoading) {
      // Store the attempted location for redirect after login
      const from = location.pathname + location.search;
      navigate('/pages/login/login3', { 
        replace: true, 
        state: { from } 
      });
    }
  }, [isAuthenticated, isLoading, isInitialized, navigate, location]);

  // Show loading spinner while initializing or checking auth
  if (!isInitialized || isLoading) {
    return (
      fallback || (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          gap={2}
        >
          <CircularProgress size={40} />
          <Typography variant="body2" color="textSecondary">
            Checking authentication...
          </Typography>
        </Box>
      )
    );
  }

  // If not authenticated, return null (navigation will handle redirect)
  if (!isAuthenticated) {
    return null;
  }

  // If authenticated, render children
  return <>{children}</>;
};

export default AuthGuard;
