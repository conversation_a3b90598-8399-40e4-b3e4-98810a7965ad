/**
 * Guest Guard Component (Simplified)
 * Redirects authenticated users away from auth pages (login, register)
 */

import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import directAuthService from '../../services/DirectAuthService';

interface GuestGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const GuestGuard: React.FC<GuestGuardProps> = ({ children, fallback }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = () => {
      try {
        const { isAuthenticated } = directAuthService.initializeAuth();
        setIsAuthenticated(isAuthenticated);

        if (isAuthenticated) {
          // Get the intended destination from location state, or default to dashboard
          const from = (location.state as any)?.from || '/dashboard';
          navigate(from, { replace: true });
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [navigate, location]);

  // Show loading spinner while checking auth
  if (isLoading) {
    return (
      fallback || (
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          justifyContent="center"
          minHeight="100vh"
          gap={2}
        >
          <CircularProgress size={40} />
          <Typography variant="body2" color="textSecondary">
            Checking authentication...
          </Typography>
        </Box>
      )
    );
  }

  // If authenticated, return null (navigation will handle redirect)
  if (isAuthenticated) {
    return null;
  }

  // If not authenticated, render children (auth forms)
  return <>{children}</>;
};

export default GuestGuard;
