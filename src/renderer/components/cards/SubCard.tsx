/* eslint-disable react/require-default-props */
import React from 'react';
import Card, { CardProps } from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardHeader from '@mui/material/CardHeader';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';

type SubCardProps  = CardProps & {
  children?: any;
  content?: boolean;
  contentClass?: string;
  darkTitle?: boolean;
  sx?: object;
  contentSX?: object;
  title?: string | React.ReactNode;
} & React.HTMLAttributes<HTMLDivElement> & React.RefAttributes<HTMLDivElement>;

const SubCard = React.forwardRef<HTMLDivElement, SubCardProps>(
  ({ children,
    content,
    contentClass,
    darkTitle,
    sx = {},
    contentSX =
    {},
    title,
    ...others }, ref) => {
    const defaultShadow = '0 2px 14px 0 rgb(32 40 45 / 8%)';

    return (
      <Card 
        ref={ref}
        {...others}
        sx={{
          border: '1px solid',
          color: 'text.secondary',
          borderColor: 'card.borderColor',
          bgcolor: 'primary.card',
          ':hover': {
            boxShadow: defaultShadow
          },
          ...sx
        }}
        >

        {!darkTitle && title && <CardHeader sx={{ p: 2.5 }} title={<Typography variant="h5">{title}</Typography>} />}
        {darkTitle && title && <CardHeader sx={{ p: 2.5 }} title={<Typography variant="h4">{title}</Typography>} />}

        {title && <Divider />}

        {content && (
          <CardContent sx={{ p: 2.5, ...contentSX }} className={contentClass || ''}>
            { children }
          </CardContent>
        )}
        {!content && children}
      </Card>
    );
  }
);


export default SubCard;
