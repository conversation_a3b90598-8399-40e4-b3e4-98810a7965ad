/* eslint-disable react/no-array-index-key */
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Grid from '@mui/material/Grid';
import Skeleton from '@mui/material/Skeleton';

export default function CampaignDetailSkeleton() {
  return (
    <Box>
      {/* Header Skeleton */}
      <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 3 }}>
        <Skeleton variant="circular" width={48} height={48} />
        <Skeleton variant="text" width={300} height={40} />
      </Stack>

      {/* Campaign Info Skeleton */}
      <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: 3 }}>
        <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
          <Skeleton variant="circular" width={56} height={56} />
          <Box sx={{ flex: 1 }}>
            <Skeleton variant="text" width="70%" height={32} />
            <Skeleton variant="text" width="50%" height={24} />
          </Box>
        </Stack>
        <Grid container spacing={2}>
          <Grid size={6}>
            <Skeleton
              variant="rectangular"
              height={80}
              sx={{ borderRadius: 2 }}
            />
          </Grid>
          <Grid size={6}>
            <Skeleton
              variant="rectangular"
              height={80}
              sx={{ borderRadius: 2 }}
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Configuration Skeleton */}
      <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: 3 }}>
        <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
          <Skeleton variant="circular" width={56} height={56} />
          <Skeleton variant="text" width={200} height={32} />
        </Stack>
        <Grid container spacing={2}>
          {[...Array(3)].map((_, index) => (
            <Grid size={4} key={index}>
              <Skeleton
                variant="rectangular"
                height={100}
                sx={{ borderRadius: 2 }}
              />
            </Grid>
          ))}
        </Grid>
      </Paper>

      {/* Additional Sections Skeleton */}
      {[...Array(3)].map((_, index) => (
        <Paper key={index} elevation={1} sx={{ p: 3, mb: 3, borderRadius: 3 }}>
          <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
            <Skeleton variant="circular" width={56} height={56} />
            <Skeleton variant="text" width={150} height={32} />
          </Stack>
          <Skeleton
            variant="rectangular"
            height={200}
            sx={{ borderRadius: 2 }}
          />
        </Paper>
      ))}
    </Box>
  );
}
