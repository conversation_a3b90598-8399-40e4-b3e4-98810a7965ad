import { ComponentType, lazy } from 'react';
import MainLayout from '../layout/MainLayout';
import Loadable from '../components/Loadable';

const AccountListWrapper = Loadable(
  lazy(
    () =>
      import('../views/accounts/index.tsx') as unknown as Promise<{
        default: ComponentType<any>;
      }>,
  ),
);

const CampaignWrapper = Loadable(
  lazy(
    () =>
      import('../views/campaigns/index.tsx') as unknown as Promise<{
        default: ComponentType<any>;
      }>,
  ),
);

const AddEditCampaignWrapper = Loadable(
  lazy(
    () =>
      import(
        '../views/campaigns/common/AddEditWrapper.tsx'
      ) as unknown as Promise<{
        default: ComponentType<any>;
      }>,
  ),
);

const ScanGroup = Loadable(
  lazy(
    () =>
      import('../views/campaigns/scanGroup/index.tsx') as unknown as Promise<{
        default: ComponentType<any>;
      }>,
  ),
);

const ScanInteraction = Loadable(
  lazy(
    () =>
      import(
        '../views/campaigns/scanInteraction/index.tsx'
      ) as unknown as Promise<{
        default: ComponentType<any>;
      }>,
  ),
);

const CampaignDetail = Loadable(
  lazy(
    () =>
      import('../views/campaigns/campaign/index.tsx') as unknown as Promise<{
        default: ComponentType<any>;
      }>,
  ),
);

// ==============================|| MAIN ROUTING ||============================== //

const MainRoutes = {
  path: '/',
  element: <MainLayout />,
  children: [
    {
      path: '/',
      element: <AccountListWrapper />,
    },
    {
      path: 'dashboard',
      children: [
        {
          path: '',
          element: <AccountListWrapper />,
        },
      ],
    },
    {
      path: 'campaign',
      element: <CampaignWrapper />,
      children: [
        { path: '', element: null },
        { path: ':type', element: null },
        { path: ':type/add', element: <AddEditCampaignWrapper /> },
        { path: ':type/:id/edit', element: <AddEditCampaignWrapper /> },
        { path: 'detail/:id', element: <CampaignDetail /> },
        { path: 'scan-group', element: <ScanGroup /> },
        { path: 'scan-interaction', element: <ScanInteraction /> },
      ],
    },
  ],
};

export default MainRoutes;
