import { ComponentType, lazy } from 'react';
import Loadable from '../components/Loadable';
import MinimalLayout from '../layout/MinimalLayout';
import GuestGuard from '../components/auth/GuestGuard';

const AuthLogin3 = Loadable(lazy(() => import('../views/pages/authentication3/Login3.tsx') as unknown as Promise<{ default: ComponentType<any>}>));
const AuthRegister3 = Loadable(lazy(() => import('../views/pages/authentication3/Register3.tsx') as unknown as Promise<{ default: ComponentType<any>}>));

const AuthenticationRoutes = {
  path: '/',
  element: <MinimalLayout />,
  children: [
    {
      path: '/pages/login/login3',
      element: (
        <GuestGuard>
          <AuthLogin3 />
        </GuestGuard>
      )
    },
    {
      path: '/pages/register/register3',
      element: (
        <GuestGuard>
          <AuthRegister3 />
        </GuestGuard>
      )
    }
  ]
};

export default AuthenticationRoutes;
