import { Page } from "puppeteer-core";

export type FBUserLoginRequest = {
  id: string;
  username: string;
  password: string;
  profileId: string;
}

export type FBUserLoginResponse = {
  success: boolean;
  userId?: string;
  error?: string;
}

export type FBLogged = {
  success: boolean;
  page: Page | null;
  user? : FBUser;
  error?: string;
}


export type FBUser = {
  userId: string;
  profilePath: string;
  username?: string;
}