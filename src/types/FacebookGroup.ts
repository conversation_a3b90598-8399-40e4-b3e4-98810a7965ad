export type FBGroup = {
  uid: string;
  groupName: string;
  url: string;
  privacy?: 'OPEN' | 'CLOSED' | 'SECRET';
  members?: string;
  location?: string;
}

export type FBContentPostRequest = {
  message?: string;
  filePaths?: Array<string>;
}

export type FBSearchGroupsResponse = {
  success: boolean;
  groups?: Array<FBGroup>;
  error?: string;
}

export type FBGroupPost = {
  groupId: string;
  postId: string;
  status: 'pending' | 'public' | 'declined' | 'deleted' | 'error';
}