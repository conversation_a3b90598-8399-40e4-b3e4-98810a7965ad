/**
 * Global type declarations for Electron APIs exposed through preload
 */

import { IFBUser } from '../interfaces/IFacebookUser';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  LogoutResponse,
  RefreshTokenResponse,
  SyncSessionResponse,
  User,
} from '../interfaces/IAuthentication';

declare global {
  interface Window {
    // Existing APIs
    api: {
      login: (data: IFBUser) => Promise<any>;
      register: (data: IFBUser) => Promise<any>;
    };

    // Authentication API
    authAPI: {
      login: (data: LoginRequest) => Promise<LoginResponse>;
      register: (data: RegisterRequest) => Promise<RegisterResponse>;
      logout: () => Promise<LogoutResponse>;
      refreshToken: () => Promise<RefreshTokenResponse>;
      syncSession: (sessionId: string) => Promise<SyncSessionResponse>;
      getCurrentUser: () => Promise<User | null>;
      checkStatus: () => Promise<{ isAuthenticated: boolean; user: User | null }>;
      initialize: () => Promise<{ isAuthenticated: boolean; user: User | null }>;
      setEnvironment: (environment: string) => Promise<{
        success: boolean;
        message?: string;
        error?: string;
        currentEnv?: string;
        apiHost?: string;
        crmApiHost?: string;
      }>;
      getEnvironment: () => Promise<{
        success: boolean;
        environment?: string;
        apiHost?: string;
        crmApiHost?: string;
        isAuthenticated?: boolean;
        error?: string;
      }>;
    };

    // Existing Facebook Web API
    facebookWeb: {
      login: (data: {
        username: string;
        passwword: string;
        profileId: string;
      }) => Promise<any>;
      searchGroups: (profileId: string, value: string) => Promise<any>;
      start: (ListUser: IFBUser[], id: number) => Promise<any>;
      stopbycampaign: (campaignId: string) => Promise<any>;
      // ... other existing methods
    };

    // Existing Account API
    account: {
      findByUserId: (userId: string) => Promise<any>;
      getUser: (id: string | number) => Promise<any>;
      createUser: (data: {
        username: string;
        password: string;
        profileId: string;
        categoryId: number;
        userId: string;
        status: string;
      }) => Promise<any>;
      getAllUsers: (search?: string) => Promise<any>;
      // ... other existing methods
    };

    // Existing Campaigns API
    Campaigns: {
      createCampaign: (data: any) => Promise<any>;
      saveCampaignConfig: (data: any) => Promise<any>;
      saveCampaignImage: (data: any) => Promise<any>;
      saveCampaignGroup: (data: any) => Promise<any>;
      getCampaign: (id: string) => Promise<any>;
      getuserpost: (groupID: string) => Promise<any>;
      // ... other existing methods
    };

    // Existing Electron API
    electronAPI: {
      dialog: {
        showOpenDialog: (options: any) => Promise<any>;
      };
      fs: {
        readFile: (filePath: string) => Promise<any>;
      };
    };
  }
}

export {};
