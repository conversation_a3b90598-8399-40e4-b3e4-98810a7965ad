import { contextBridge, ipc<PERSON><PERSON><PERSON>, IpcRendererEvent } from 'electron';
import { IFBUser } from '../interfaces/IFacebookUser';
import { Category } from '../interfaces/Categorys';
import {
  CampaignConfigurationRequest,
  CampaignDetailsRequest,
  CampaignProgress,
  CampaignRequest,
  GroupRequest,
  ImageRequest,
} from '../interfaces/Campaigns';

type ProgressCallback = (data: CampaignProgress) => void;
type CleanupFunction = () => void;

contextBridge.exposeInMainWorld('api', {
  login: (data: IFBUser) => ipcRenderer.invoke('account:login', data),
  register: (data: IFBUser) => ipcRenderer.invoke('account:register', data),
});

// Authentication API
contextBridge.exposeInMainWorld('authAPI', {
  login: async (data: { email: string; password: string }) => {
    const result = await ipcRenderer.invoke('auth:login', data);
    return result;
  },

  register: async (data: {
    email: string;
    password: string;
    password_confirmation: string;
    phone: string;
    full_name: string;
    app_code: string;
    referal_code: string;
  }) => {
    const result = await ipcRenderer.invoke('auth:register', data);
    return result;
  },

  logout: async () => {
    const result = await ipcRenderer.invoke('auth:logout');
    return result;
  },

  refreshToken: async () => {
    const result = await ipcRenderer.invoke('auth:refreshToken');
    return result;
  },

  syncSession: async (sessionId: string) => {
    const result = await ipcRenderer.invoke('auth:syncSession', sessionId);
    return result;
  },

  getCurrentUser: async () => {
    const result = await ipcRenderer.invoke('auth:getCurrentUser');
    return result;
  },

  checkStatus: async () => {
    const result = await ipcRenderer.invoke('auth:checkStatus');
    return result;
  },

  initialize: async () => {
    const result = await ipcRenderer.invoke('auth:initialize');
    return result;
  },

  setEnvironment: async (environment: string) => {
    const result = await ipcRenderer.invoke('auth:setEnvironment', environment);
    return result;
  },

  getEnvironment: async () => {
    const result = await ipcRenderer.invoke('auth:getEnvironment');
    return result;
  },
});

contextBridge.exposeInMainWorld('facebookWeb', {
  login: async (data: {
    username: string;
    passwword: string;
    profileId: string;
  }) => {
    const result = await ipcRenderer.invoke('facebook:login', data);
    return result;
  },

  searchGroups: async (profileId: string, value: string) => {
    const results = await ipcRenderer.invoke(
      'facebook:searchGroups',
      profileId,
      value,
    );
    return results;
  },

  start: async (ListUser: IFBUser[], id: number) => {
    const results = await ipcRenderer.invoke('facebook:start', ListUser, id);
    return results;
  },

  stopbycampaign: async (campaignId: string) => {
    const results = await ipcRenderer.invoke(
      'facebook:stopbycampaign',
      campaignId,
    );
    return results;
  },

  stopAll: async () => {
    const results = await ipcRenderer.invoke('facebook:stopAll');
    return results;
  },

  onProgress: (callback: ProgressCallback): CleanupFunction => {
    const listener = (
      _event: IpcRendererEvent,
      data: { message: string; campaignId: string; action?: string },
    ) => {
      callback(data);
    };

    ipcRenderer.on('campaign-log', listener);
    return () => {
      ipcRenderer.removeListener('campaign-log', listener);
    };
  },

  createUserpost: async (ListUser: IFBUser[], campaignId: number) => {
    const results = await ipcRenderer.invoke('facebook:createUserpost', ListUser, campaignId);
    return results;
  },

  copyProfileImage: async (sourceImagePath: string[]) => {
    const results = await ipcRenderer.invoke('facebook:copyProfileImage', sourceImagePath);
    return results;
  },
});

contextBridge.exposeInMainWorld('category', {
  getAllCategorys: async (resourceType?: string) => {
    const result = await ipcRenderer.invoke(
      'category:getAllCategorys',
      resourceType,
    );
    return result;
  },

  createCategory: async (data: Category) => {
    const results = await ipcRenderer.invoke('category:createCategory', data);
    return results;
  },

  deleteCategory: async (id: number) => {
    const results = await ipcRenderer.invoke('category:deleteCategory', id);
    return results;
  },
});

contextBridge.exposeInMainWorld('account', {
  findByUserId: async (userId: string) => {
    const result = await ipcRenderer.invoke('account:findByUserId', userId);
    return result;
  },

  getUser: async (id: string | number) => {
    const result = await ipcRenderer.invoke('account:getUser', id);
    return result;
  },

  createUser: async (data: {
    username: string;
    password: string;
    profileId: string;
    categoryId: number;
    userId: string;
    status: string;
  }) => {
    const result = await ipcRenderer.invoke('account:createUser', data);
    return result;
  },

  getAllUsers: async (search?: string) => {
    const result = await ipcRenderer.invoke('account:getAllUsers', search);
    return result;
  },

  deleteUser: async (id: string | number) => {
    const result = await ipcRenderer.invoke('account:deleteUser', id);
    return result;
  },

  updateUser: async (data: IFBUser) => {
    const result = await ipcRenderer.invoke('account:updateUser', data);
    return result;
  },

  launchProfilePage: async (id: string | number) => {
    const result = await ipcRenderer.invoke('account:launchProfilePage', id);
    return result;
  },

  getUserByStatus: async (status: string) => {
    const result = await ipcRenderer.invoke('account:getUserByStatus', status);
    return result;
  },

  bulkDeleteUsers: async (ids: string[]) => {
    const result = await ipcRenderer.invoke('account:bulkDeleteUsers', ids);
    return result;
  },
});

contextBridge.exposeInMainWorld('Campaigns', {
  createCampaign: async (data: CampaignDetailsRequest) => {
    const result = await ipcRenderer.invoke('Campaigns:createCampaign', data);
    return result;
  },

  saveCampaignConfig: async (data: CampaignConfigurationRequest) => {
    const result = await ipcRenderer.invoke(
      'Campaigns:saveCampaignConfig',
      data,
    );
    return result;
  },

  saveCampaignImage: async (data: ImageRequest) => {
    const result = await ipcRenderer.invoke(
      'Campaigns:saveCampaignImage',
      data,
    );
    return result;
  },

  saveCampaignGroup: async (data: GroupRequest) => {
    const result = await ipcRenderer.invoke(
      'Campaigns:saveCampaignGroup',
      data,
    );
    return result;
  },

  getAllCampaigns: async (type?: string) => {
    const result = await ipcRenderer.invoke('Campaigns:getAllCampaigns', type);
    return result;
  },

  getCampaignDetails: async (id: string) => {
    const result = await ipcRenderer.invoke('Campaigns:getCampaignDetails', id);
    return result;
  },

  updateCampaign: async (data: CampaignRequest) => {
    const result = await ipcRenderer.invoke('Campaigns:updateCampaign', data);
    return result;
  },

  deleteCampaign: async (id: string) => {
    const result = await ipcRenderer.invoke('Campaigns:deleteCampaign', id);
    return result;
  },

  bulkDeleteCampaigns: async (ids: string[]) => {
    const result = await ipcRenderer.invoke(
      'Campaigns:bulkDeleteCampaigns',
      ids,
    );
    return result;
  },

  getCampaign: async (id: string) => {
    const result = await ipcRenderer.invoke('Campaigns:getCampaign', id);
    return result;
  },

  getuserpost: async (groupID: string) => {
    const result = await ipcRenderer.invoke('Campaigns:getuserpost', groupID);
    return result;
  },
});

// Expose Electron APIs for file operations
contextBridge.exposeInMainWorld('electronAPI', {
  dialog: {
    showOpenDialog: async (options: any) => {
      const result = await ipcRenderer.invoke('dialog:showOpenDialog', options);
      return result;
    },
  },
  fs: {
    readFile: async (filePath: string) => {
      const result = await ipcRenderer.invoke('fs:readFile', filePath);
      return result;
    },
  },
});
