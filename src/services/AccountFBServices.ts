/* eslint-disable @typescript-eslint/no-unused-vars */
import { Database } from 'better-sqlite3';
import { IFBUser, IFBUserRequest, IFBUserResponse } from '../interfaces/IFacebookUser';
import log from '../utils/logs';
import DatabaseManager from '../db/sqlite';

export default class AccountFBServices {

  private db: Database;

  public static STATUS_ACTIVE = 'active';

  public static STATUS_INACTIVE = 'inactive';

  public static STATUS_RUNNING = 'running';

  constructor() {
    this.db = DatabaseManager.getInstance().getDb();
  }

  findByUserId(userId: string): IFBUser|null {
    try {
      return this.db.prepare(`SELECT * FROM users WHERE userId = ?`)
        .get(userId) as IFBUser;
    } catch (error) {
      return null;
    }
  }

  createUser({
    username,
    password,
    profileId,
    categoryId,
    userId,
    status
  }: IFBUserRequest): number| null {

    try {
      const insertStmt = this.db.prepare(`
      INSERT INTO users (username, password, profileId, categoryId, userId, status) VALUES (?, ?, ?, ?, ?, ?)
      `);

      const result = insertStmt.run(
        username,
        password,
        profileId,
        categoryId,
        userId || null,
        status
      );

      return result.lastInsertRowid as number;
    } catch (error) {
      log.error("An error occurred when create user", error);
      return null;
    }
  }

  getAllUsers(
    search: string | undefined,
    page: number = 1,
    orderBy: keyof IFBUser = 'id',
    order: 'ASC' | 'DESC' = 'DESC',
    limit: number = 25
  ): IFBUserResponse[] {

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    let query = `
      SELECT
        id,
        userId,
        username,
        password,
        status,
        profileId,
        userId,
        category_name
      FROM users
      LEFT JOIN categories ON categoryId = category_id
    `;

    const params: any[] = [];
    if (search && search.trim() !== '') {
      query += ` WHERE username LIKE ? OR userId LIKE ?`;
      params.push(`%${search}%`, `%${search}%`);
    }

    query += ` ORDER BY ${orderBy} ${order} LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    const result = this.db
      .prepare(query)
      .all(...params) as IFBUserResponse[];
    return result;
  }

  deleteUser(id: string): boolean {
    try {
      this.db.prepare(`DELETE FROM users WHERE id = ?`).run(id);
      return true;
    } catch (error) {
      log.error("An error occurred when delete user", error);
      return false;
    }
  }

  updateUser(data: IFBUser): boolean {
    try {
      const updates: string[] = [];
      const values: any[] = [];

      if (data.username !== undefined && data.username !== null && data.username !== '') {
        updates.push('username = ?');
        values.push(data.username);
      }
      if (data.password !== undefined && data.password !== null && data.password !== '') {
        updates.push('password = ?');
        values.push(data.password);
      }
      if (data.profileId !== undefined && data.profileId !== null && data.profileId !== '') {
        updates.push('profileId = ?');
        values.push(data.profileId);
      }
      if (data.status !== undefined && data.status !== null && data.status !== '') {
        updates.push('status = ?');
        values.push(data.status);
      }
      if (Number.isNaN(data.categoryId)) {
        updates.push('categoryId = ?');
        values.push(data.categoryId);
      }
      if (data.userId !== undefined && data.userId !== null && data.userId !== '') {
        updates.push('userId = ?');
        values.push(data.userId);
      }

      if (updates.length === 0) {
        log.warn('No valid fields to update for user', data.id);
        return false;
      }

      values.push(data.id);

      const query = `UPDATE users SET ${updates.join(', ')} WHERE id = ?`;

      this.db.prepare(query).run(...values);
      return true;

    } catch (error) {
      log.error("An error occurred when update user", error);
      return false;
    }
  }

  getUser(id: number | string): IFBUserResponse | null {
    try {
      return this.db
        .prepare(`
          SELECT
            id,
            username,
            password,
            profileId,
            userId,
            status,
            categoryId,
            category_name
          FROM users
          LEFT JOIN categories ON categoryId = category_id
          WHERE id = ?
        `).get(id) as IFBUserResponse;
    } catch (error) {
      return null;
    }
  }

  async getUserbyprofileId(profileIds: string[]): Promise<IFBUser[]> {
    try {
      const placeholders = profileIds.map(() => '?').join(', ');
      const stmt = this.db.prepare(`SELECT * FROM users WHERE profileId IN (${placeholders})`);
      const users = stmt.all(...profileIds) as IFBUser[];

      return users;
    } catch (error) {
      console.error("Error fetching users:", error);
      return [];
    }
  }

  getUserByStatus(status: string): IFBUserResponse[] {
    const stmt = this.db.prepare(`
      SELECT
        id,
        username,
        password,
        profileId,
        userId,
        status,
        categoryId,
        category_name
      FROM users
      LEFT JOIN categories ON categoryId = category_id
      WHERE status = ?
    `);

    return stmt.all(status) as IFBUserResponse[];
  }

  bulkDeleteUsers(ids: (string | number)[]): boolean {
    if (ids.length === 0) {
      log.warn('No IDs provided for bulk delete');
      return false;
    }

    const placeholders = ids.map(() => '?').join(', ');
    const query = `DELETE FROM users WHERE id IN (${placeholders})`;

    try {
      this.db.prepare(query).run(...ids);
      return true;
    } catch (error) {
      log.error("An error occurred when bulk delete users", error);
      return false;
    }
  }
}
