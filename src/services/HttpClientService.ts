/**
 * HTTP Client Service for API communication
 * Provides robust HTTP client with error handling, logging, and authentication
 */

import log from '../utils/logs';
import ApiConfig from '../config/ApiConfig';
import { HttpResponse, ErrorResponse } from '../interfaces/IAuthentication';

export interface RequestOptions {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  isFormData?: boolean;
}

export class HttpClientService {
  private defaultTimeout = 30000; // 30 seconds

  /**
   * Make HTTP request
   */
  async request<T = any>(url: string, options: RequestOptions): Promise<HttpResponse<T>> {
    const { method, headers = {}, body, timeout = this.defaultTimeout, isFormData = false } = options;

    try {
      // Prepare headers
      const requestHeaders: Record<string, string> = {
        ...headers,
        ...ApiConfig.getAuthHeader(),
      };

      // Don't set Content-Type for FormData, let the browser set it with boundary
      if (!isFormData && method !== 'GET') {
        requestHeaders['Content-Type'] = 'application/json';
      }

      // Prepare request body
      let requestBody: any = null;
      if (body && method !== 'GET') {
        if (isFormData) {
          requestBody = body; // FormData object
        } else {
          requestBody = JSON.stringify(body);
        }
      }

      log.info(`HTTP ${method} ${url}`, { headers: requestHeaders, body: body });

      // Create abort controller for timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: requestBody,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // Parse response
      let responseData: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      const httpResponse: HttpResponse<T> = {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: this.parseHeaders(response.headers),
      };

      if (!response.ok) {
        log.error(`HTTP ${method} ${url} failed`, {
          status: response.status,
          statusText: response.statusText,
          data: responseData,
        });

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      log.info(`HTTP ${method} ${url} success`, {
        status: response.status,
        data: responseData,
      });

      return httpResponse;
    } catch (error: any) {
      log.error(`HTTP ${method} ${url} error`, error);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      throw error;
    }
  }

  /**
   * GET request
   */
  async get<T = any>(url: string, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>(url, { method: 'GET', headers });
  }

  /**
   * POST request with JSON body
   */
  async post<T = any>(url: string, body?: any, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>(url, { method: 'POST', body, headers });
  }

  /**
   * POST request with FormData (multipart/form-data)
   */
  async postFormData<T = any>(url: string, formData: FormData, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>(url, { 
      method: 'POST', 
      body: formData, 
      headers, 
      isFormData: true 
    });
  }

  /**
   * PUT request
   */
  async put<T = any>(url: string, body?: any, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>(url, { method: 'PUT', body, headers });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(url: string, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>(url, { method: 'DELETE', headers });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(url: string, body?: any, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>(url, { method: 'PATCH', body, headers });
  }

  /**
   * Create FormData from object (similar to C# MultipartFormDataContent)
   */
  createFormData(data: Record<string, string | Blob | File>): FormData {
    const formData = new FormData();
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        formData.append(key, value);
      }
    });

    return formData;
  }

  /**
   * Parse response headers
   */
  private parseHeaders(headers: Headers): Record<string, string> {
    const result: Record<string, string> = {};
    headers.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }

  /**
   * Handle API error response
   */
  handleApiError(error: any): ErrorResponse {
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data?.message || error.response.statusText || 'Server error',
        code: error.response.data?.code,
        details: error.response.data,
        status: error.response.status,
      };
    } else if (error.request) {
      // Network error
      return {
        message: 'Network error - please check your connection',
        code: 'NETWORK_ERROR',
      };
    } else {
      // Other error
      return {
        message: error.message || 'An unexpected error occurred',
        code: 'UNKNOWN_ERROR',
      };
    }
  }
}

// Export singleton instance
export const httpClient = new HttpClientService();
export default httpClient;
