/**
 * Authentication Service
 * Implements authentication functionality mirroring the C# implementation
 */

import log from '../utils/logs';
import ApiConfig from '../config/ApiConfig';
import httpClient from './HttpClientService';
import {
  IAuthenticationService,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  LogoutResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  SyncSessionRequest,
  SyncSessionResponse,
  User,
} from '../interfaces/IAuthentication';

export class AuthenticationService implements IAuthenticationService {
  /**
   * User login
   */
  async login(request: LoginRequest): Promise<LoginResponse> {
    try {
      log.info('Attempting user login', { email: request.email });

      const url = ApiConfig.getApiUrl(ApiConfig.loginUrl);
      const response = await httpClient.post<any>(url, request);

      if (response.data && response.data.success) {
        // Store authentication data
        if (response.data.data?.token) {
          ApiConfig.bearerToken = response.data.data.token;
        }
        if (response.data.data?.sessionId) {
          ApiConfig.sessionId = response.data.data.sessionId;
        }

        log.info('User login successful', { userId: response.data.data?.user?.id });

        return {
          success: true,
          data: response.data.data,
          message: response.data.message || 'Login successful',
        };
      } else {
        log.error('Login failed', response.data);
        return {
          success: false,
          error: response.data?.message || 'Login failed',
        };
      }
    } catch (error: any) {
      log.error('Login error', error);
      const errorResponse = httpClient.handleApiError(error);
      return {
        success: false,
        error: errorResponse.message,
      };
    }
  }

  /**
   * User registration (mirroring C# multipart form data implementation)
   */
  async register(request: RegisterRequest): Promise<RegisterResponse> {
    try {
      log.info('Attempting user registration', { email: request.email });

      // Create FormData similar to C# MultipartFormDataContent
      const formData = httpClient.createFormData({
        email: request.email,
        password: request.password,
        password_confirmation: request.password_confirmation,
        phone: request.phone,
        full_name: request.full_name,
        app_code: request.app_code,
        referal_code: request.referal_code,
      });

      const url = ApiConfig.getApiUrl(ApiConfig.registerUrl);
      const response = await httpClient.postFormData<any>(url, formData);

      if (response.data && response.data.success) {
        log.info('User registration successful', { userId: response.data.data?.user?.id });

        return {
          success: true,
          data: response.data.data,
          message: response.data.message || 'Registration successful',
        };
      } else {
        log.error('Registration failed', response.data);
        return {
          success: false,
          error: response.data?.message || 'Registration failed',
        };
      }
    } catch (error: any) {
      log.error('Registration error', error);
      const errorResponse = httpClient.handleApiError(error);
      return {
        success: false,
        error: errorResponse.message,
      };
    }
  }

  /**
   * User logout
   */
  async logout(): Promise<LogoutResponse> {
    try {
      log.info('Attempting user logout');

      const url = ApiConfig.getApiUrl(ApiConfig.logoutUrl);
      const response = await httpClient.post<any>(url);

      // Clear authentication data regardless of response
      this.clearAuthData();

      if (response.data && response.data.success) {
        log.info('User logout successful');
        return {
          success: true,
          message: response.data.message || 'Logout successful',
        };
      } else {
        log.warn('Logout response not successful, but clearing local auth data');
        return {
          success: true,
          message: 'Logged out locally',
        };
      }
    } catch (error: any) {
      log.error('Logout error', error);
      // Still clear local auth data even if server request fails
      this.clearAuthData();
      return {
        success: true,
        message: 'Logged out locally',
      };
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(request?: RefreshTokenRequest): Promise<RefreshTokenResponse> {
    try {
      log.info('Attempting token refresh');

      const url = ApiConfig.getApiUrl(ApiConfig.refreshTokenUrl);
      const response = await httpClient.post<any>(url, request || {});

      if (response.data && response.data.success) {
        // Update stored token
        if (response.data.data?.token) {
          ApiConfig.bearerToken = response.data.data.token;
        }

        log.info('Token refresh successful');
        return {
          success: true,
          data: response.data.data,
          message: response.data.message || 'Token refreshed successfully',
        };
      } else {
        log.error('Token refresh failed', response.data);
        return {
          success: false,
          error: response.data?.message || 'Token refresh failed',
        };
      }
    } catch (error: any) {
      log.error('Token refresh error', error);
      const errorResponse = httpClient.handleApiError(error);
      return {
        success: false,
        error: errorResponse.message,
      };
    }
  }

  /**
   * Sync session with server
   */
  async syncSession(request: SyncSessionRequest): Promise<SyncSessionResponse> {
    try {
      log.info('Attempting session sync', { sessionId: request.sessionId });

      const url = ApiConfig.getApiUrl(ApiConfig.syncSessionUrl);
      const response = await httpClient.post<any>(url, request);

      if (response.data && response.data.success) {
        // Update authentication data
        if (response.data.data?.token) {
          ApiConfig.bearerToken = response.data.data.token;
        }
        if (response.data.data?.sessionId) {
          ApiConfig.sessionId = response.data.data.sessionId;
        }

        log.info('Session sync successful');
        return {
          success: true,
          data: response.data.data,
          message: response.data.message || 'Session synced successfully',
        };
      } else {
        log.error('Session sync failed', response.data);
        return {
          success: false,
          error: response.data?.message || 'Session sync failed',
        };
      }
    } catch (error: any) {
      log.error('Session sync error', error);
      const errorResponse = httpClient.handleApiError(error);
      return {
        success: false,
        error: errorResponse.message,
      };
    }
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      if (!ApiConfig.isAuthenticated) {
        return null;
      }

      // This would typically be a separate endpoint like /auth/me
      // For now, we'll return null and implement when the endpoint is available
      log.info('Getting current user - endpoint not implemented yet');
      return null;
    } catch (error: any) {
      log.error('Get current user error', error);
      return null;
    }
  }

  /**
   * Check if current token is valid
   */
  isTokenValid(): boolean {
    return ApiConfig.isAuthenticated;
  }

  /**
   * Clear authentication data
   */
  clearAuthData(): void {
    log.info('Clearing authentication data');
    ApiConfig.clearAuth();
  }
}

// Export singleton instance
export const authenticationService = new AuthenticationService();
export default authenticationService;
