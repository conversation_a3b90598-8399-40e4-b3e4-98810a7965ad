import { app, screen } from 'electron';
import puppeteer, { Page } from 'puppeteer-core';
import path from 'path';
import fs from 'fs-extra';
import log from '../../utils/logs';

/**
 * Base browser
 */
export default class FacebookBrowser {
  protected facebookUrl: string = 'https://www.facebook.com';

  protected twoFaPath: string = '/two_step_verification/two_factor/';

  private configs: Array<string> = [
    '-force-device-scale-factor=0.9',
    '--test-type',
    '--disable-dev-shm-usage',
    '--disable-gpu', // Tắt GPU để tránh lỗi đồ họa trong chế độ headless
    '--disable-notifications', // Tắt thông báo từ Facebook
    '--disable-blink-features=AutomationControlled', // Ẩn dấu hiệu web driver
    '--disable-background-timer-throttling', // Ngăn trình duyệt tạm dừng khi không hoạt động
    '--disable-backgrounding-occluded-windows', // <PERSON><PERSON>n tối ưu hóa cửa sổ bị che
    '--disable-renderer-backgrounding', // Ngăn tối ưu hóa trình render
    '--disable-client-side-phishing-detection', // Tắt kiểm tra phishing
    '--disable-default-apps', // Tắt ứng dụng mặc định
  ];

  private ignoreDefaultArgs: Array<string> = ['--enable-automation'];

  private userAgent: string =
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36';

  private basePath: string = path.join(app.getAppPath(), 'browsers');

  /**
   * Get browser path
   * @returns browser path
   */
  protected getChromiumPath(): string | null {
    const { platform } = process;
    if (platform === 'win32') {
      return path.join(this.basePath, 'win/chrome/chrome.exe');
    }

    if (platform === 'darwin') {
      return path.join(
        this.basePath,
        'mac/chromium/Chromium.app/Contents/MacOS/Chromium',
      );
    }

    return null;
  }

  /**
   * Get profile dir
   * @param profileId
   * @returns
   */
  protected static getProfileDir(profileId: string): string {
    return path.join(__dirname, '../../..', 'userData', `profile-${profileId}`);
  }

  /**
   * Check profile session
   * @param profileDir
   * @returns boolean
   */
  protected static async hasValidProfile(profileDir: string): Promise<boolean> {
    const stats = await fs.stat(profileDir);
    if (!stats.isDirectory()) return false;
    return true;
  }

  /**
   * Window size arguments for Puppeteer
   * @returns
   */
  private getWindowSizeArgs(): string {
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;
    const winWidth = Math.floor(width / 2);
    const winHeight = height;
    return `--window-size=${winWidth},${winHeight}`;
  }

  private getConfigArgs(): Array<string> {
    const args = [...this.configs];
    args.push(this.getWindowSizeArgs());
    if (process.platform === 'win32') {
      args.push('--disable-web-security'); // Tắt bảo mật web trên Windows
    }
    return args;
  }

  /**
   * Get or create browser for a specific id
   * @param id - User profile identifier
   * @param headless - Headless mode
   * @returns BrowserContext or null
   */
  protected async launchBrowser(
    id: string,
    headless: boolean = true,
  ): Promise<Page | null> {
    const profileDir = FacebookBrowser.getProfileDir(id);
    const chromePath = this.getChromiumPath();
    if (chromePath && !fs.existsSync(chromePath)) {
      log.error('Chromium executable does not exist at:', chromePath);
      return null;
    }
    if (chromePath) {
      try {
        const browser = await puppeteer.launch({
          executablePath: chromePath,
          headless,
          defaultViewport: null,
          userDataDir: profileDir,
          args: this.getConfigArgs(),
          ignoreDefaultArgs: this.ignoreDefaultArgs,
        });

        const [page] = await browser.pages();
        await page.setUserAgent(this.userAgent);
        await this.disableInfobar(page);
        return page;
      } catch (error) {
        log.error(`Failed to launch browser:`, error);
        return null;
      }
    }
    log.error('Browser does not support this OS');
    return null;
  }

  private async disableInfobar(page: Page): Promise<void> {
    try {
      await page.createCDPSession().then((session) => {
        session.send('Browser.setDockTile', { badgeLabel: '' });
      });
    } catch (error) {
      log.error('Failed to disable infobar:', error);
    }
  }

  /**
   * Close browser context for a specific userId
   * @param userId - User identifier
   */
  static async close(page: Page): Promise<void> {
    await page.close();
    const browser = page.browser();
    if (browser && browser.connected) {
      await browser.close();
    }
  }
}
