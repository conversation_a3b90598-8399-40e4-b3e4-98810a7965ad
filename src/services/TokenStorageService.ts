/**
 * Token Storage Service
 * Provides secure storage for authentication tokens and session data in Electron
 */

import { app } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import log from '../utils/logs';
import { TokenStorage, User } from '../interfaces/IAuthentication';

export class TokenStorageService {
  private readonly storageFileName = 'auth-tokens.json';
  private readonly storagePath: string;

  constructor() {
    // Use Electron's userData directory for secure storage
    const userDataPath = app.getPath('userData');
    this.storagePath = path.join(userDataPath, this.storageFileName);
  }

  /**
   * Save authentication tokens securely
   */
  async saveTokens(tokenData: TokenStorage): Promise<boolean> {
    try {
      log.info('Saving authentication tokens');

      // Ensure the directory exists
      await fs.ensureDir(path.dirname(this.storagePath));

      // In a production app, you might want to encrypt this data
      // For now, we'll store it as JSON with restricted file permissions
      const dataToStore = {
        ...tokenData,
        savedAt: new Date().toISOString(),
      };

      await fs.writeJson(this.storagePath, dataToStore, { spaces: 2 });

      // Set restrictive file permissions (owner read/write only)
      if (process.platform !== 'win32') {
        await fs.chmod(this.storagePath, 0o600);
      }

      log.info('Authentication tokens saved successfully');
      return true;
    } catch (error) {
      log.error('Failed to save authentication tokens', error);
      return false;
    }
  }

  /**
   * Load authentication tokens
   */
  async loadTokens(): Promise<TokenStorage | null> {
    try {
      if (!(await fs.pathExists(this.storagePath))) {
        log.info('No stored authentication tokens found');
        return null;
      }

      const tokenData = await fs.readJson(this.storagePath);

      // Validate token structure
      if (!this.isValidTokenData(tokenData)) {
        log.warn('Invalid token data structure, clearing storage');
        await this.clearTokens();
        return null;
      }

      // Check if token is expired
      if (this.isTokenExpired(tokenData.expiresAt)) {
        log.info('Stored token is expired, clearing storage');
        await this.clearTokens();
        return null;
      }

      log.info('Authentication tokens loaded successfully');
      return {
        token: tokenData.token,
        sessionId: tokenData.sessionId,
        expiresAt: tokenData.expiresAt,
        user: tokenData.user,
      };
    } catch (error) {
      log.error('Failed to load authentication tokens', error);
      return null;
    }
  }

  /**
   * Clear stored authentication tokens
   */
  async clearTokens(): Promise<boolean> {
    try {
      if (await fs.pathExists(this.storagePath)) {
        await fs.remove(this.storagePath);
        log.info('Authentication tokens cleared successfully');
      }
      return true;
    } catch (error) {
      log.error('Failed to clear authentication tokens', error);
      return false;
    }
  }

  /**
   * Update user information in stored tokens
   */
  async updateUser(user: User): Promise<boolean> {
    try {
      const currentTokens = await this.loadTokens();
      if (!currentTokens) {
        log.warn('No tokens to update user information');
        return false;
      }

      const updatedTokens: TokenStorage = {
        ...currentTokens,
        user,
      };

      return await this.saveTokens(updatedTokens);
    } catch (error) {
      log.error('Failed to update user information', error);
      return false;
    }
  }

  /**
   * Update token and expiration
   */
  async updateToken(token: string, expiresAt: string): Promise<boolean> {
    try {
      const currentTokens = await this.loadTokens();
      if (!currentTokens) {
        log.warn('No tokens to update');
        return false;
      }

      const updatedTokens: TokenStorage = {
        ...currentTokens,
        token,
        expiresAt,
      };

      return await this.saveTokens(updatedTokens);
    } catch (error) {
      log.error('Failed to update token', error);
      return false;
    }
  }

  /**
   * Check if stored tokens exist and are valid
   */
  async hasValidTokens(): Promise<boolean> {
    const tokens = await this.loadTokens();
    return tokens !== null;
  }

  /**
   * Get token expiration status
   */
  async getTokenExpirationInfo(): Promise<{
    isExpired: boolean;
    expiresAt: string | null;
    timeUntilExpiry: number | null;
  }> {
    const tokens = await this.loadTokens();
    
    if (!tokens) {
      return {
        isExpired: true,
        expiresAt: null,
        timeUntilExpiry: null,
      };
    }

    const expiresAt = new Date(tokens.expiresAt);
    const now = new Date();
    const isExpired = expiresAt <= now;
    const timeUntilExpiry = isExpired ? 0 : expiresAt.getTime() - now.getTime();

    return {
      isExpired,
      expiresAt: tokens.expiresAt,
      timeUntilExpiry,
    };
  }

  /**
   * Validate token data structure
   */
  private isValidTokenData(data: any): boolean {
    return (
      data &&
      typeof data.token === 'string' &&
      typeof data.sessionId === 'string' &&
      typeof data.expiresAt === 'string' &&
      data.user &&
      typeof data.user.id === 'string' &&
      typeof data.user.email === 'string'
    );
  }

  /**
   * Check if token is expired
   */
  private isTokenExpired(expiresAt: string): boolean {
    try {
      const expirationDate = new Date(expiresAt);
      const now = new Date();
      return expirationDate <= now;
    } catch (error) {
      log.error('Failed to parse expiration date', error);
      return true; // Treat invalid dates as expired
    }
  }

  /**
   * Get storage file path (for debugging)
   */
  getStoragePath(): string {
    return this.storagePath;
  }
}

// Export singleton instance
export const tokenStorageService = new TokenStorageService();
export default tokenStorageService;
