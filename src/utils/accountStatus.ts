import { alpha } from '@mui/material';
import { Theme } from '@mui/material/styles';

export type AccountStatus = 'active' | 'inactive';

export interface AccountStatusConfig {
  label: string;
  color: 'success' | 'default';
  bgColor: string;
  textColor: string;
  borderColor: string;
}

/**
 * Get status configuration for account status display
 * @param status - Account status
 * @param theme - Material-UI theme
 * @returns Status configuration object
 */
export const getAccountStatusConfig = (status: string, theme: Theme): AccountStatusConfig => {
  switch (status) {
    case 'active':
      return {
        label: 'Hoạt động',
        color: 'success' as const,
        bgColor: alpha(theme.palette.success.main || '#4caf50', 0.1),
        textColor: theme.palette.success.dark || '#2e7d32',
        borderColor: alpha(theme.palette.success.main || '#4caf50', 0.3),
      };
    case 'inactive':
    default:
      return {
        label: 'Không hoạt động',
        color: 'default' as const,
        bgColor: alpha(theme.palette.grey[500] || '#9e9e9e', 0.1),
        textColor: theme.palette.grey[700] || '#616161',
        borderColor: alpha(theme.palette.grey[500] || '#9e9e9e', 0.3),
      };
  }
};

/**
 * Filter accounts based on search query
 * @param accounts - Array of accounts
 * @param searchQuery - Search query string
 * @returns Filtered accounts
 */
export const filterAccounts = <T extends { 
  username: string; 
  category_name?: string; 
  userId?: string; 
}>(
  accounts: T[],
  searchQuery: string
): T[] => {
  if (!searchQuery) return accounts;
  
  const query = searchQuery.toLowerCase();
  return accounts.filter(
    (account) =>
      account.username.toLowerCase().includes(query) ||
      account.category_name?.toLowerCase().includes(query) ||
      account.userId?.toLowerCase().includes(query),
  );
};

/**
 * Mask password for display
 * @param password - Original password
 * @returns Masked password
 */
export const maskPassword = (password: string): string => {
  if (!password || password.length <= 3) return password;
  return password.substring(0, 3) + '*'.repeat(password.length - 3);
};

/**
 * Get account category label styling
 * @param theme - Material-UI theme
 * @returns Category chip styling
 */
export const getCategoryChipStyle = (theme: Theme) => ({
  fontSize: '0.75rem',
  borderRadius: 2,
  fontWeight: 600,
  color: '#c8975d',
});

/**
 * Get password field styling
 * @param theme - Material-UI theme
 * @param isVisible - Whether password is visible
 * @returns Password field styling
 */
export const getPasswordFieldStyle = (theme: Theme, isVisible: boolean) => ({
  container: {
    backgroundColor: alpha(theme.palette.grey[100] || '#f5f5f5', 0.8),
    borderRadius: 1,
    px: 1.5,
    py: 0.5,
    border: `1px solid ${alpha(theme.palette.grey[300] || '#e0e0e0', 0.5)}`,
  },
  text: {
    fontFamily: 'monospace',
    fontSize: '0.875rem',
    minWidth: '80px',
    color: isVisible
      ? theme.palette.text.primary
      : theme.palette.text.secondary,
    fontWeight: isVisible ? 500 : 400,
  },
  button: {
    padding: '6px',
    color: theme.palette.primary.main || '#1976d2',
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main || '#1976d2', 0.1),
      transform: 'scale(1.1)',
    },
    transition: 'all 0.2s ease-in-out',
  },
});
