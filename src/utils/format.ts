import { pad } from './helper';

export default function formatTimeLog() {
  const date = new Date();
  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1, 2);
  const day = pad(date.getDate(), 2);
  const hours = pad(date.getHours(), 2);
  const minutes = pad(date.getMinutes(), 2);
  const seconds = pad(date.getSeconds(), 2);
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * Format date
 * @param date Date or string
 * @returns date string, e.g dd/mm/yyyy or dd/mm/yyyy hh:mm
 */
export function formatDate(date: Date | string, type: 'date' | 'datetime' = 'date') {
  let dateObj = date;
  
  if (typeof date === 'string') {
    dateObj = new Date(date);
  }
  if (!(dateObj instanceof Date) || Number.isNaN(dateObj.getTime())) return '---';
  
  if (type === 'date') {
    return formatDateOnly(dateObj);
  } else {
    return formatDateTime(dateObj);
  }
}

/**
 * Format date only
 * @param date Date or string
 * @returns date string, e.g dd/mm/yyyy
 */
function formatDateOnly(dateObj: Date) {
  if (!(dateObj instanceof Date) || Number.isNaN(dateObj.getTime())) return '---';
  const day = String(dateObj.getDate()).padStart(2, '0');
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const year = dateObj.getFullYear();
  return `${day}/${month}/${year}`;
}

function formatDateTime(dateObj: Date) {
  const formatedDate = formatDateOnly(dateObj);
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  return `${formatedDate} ${hours}:${minutes}`;
}