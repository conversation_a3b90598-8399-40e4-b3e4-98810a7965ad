const phoneNumberRegex = /^0(3[2-9]|5[5689]|7[06-9]|8[1-689]|9[0-46-9])[0-9]{7}$/;
const emailRegEx = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const requiredString = (input: string | null | undefined): Array<string> => {
  if (input === null || input === undefined || input.trim() === "") {
    return ['Trường thông tin được yêu cầu'];
  }

  return [];
}

const email = (input: string | null | undefined): Array<string> => {
  if (input === null 
    || input === undefined 
    || input.trim() === "" 
    || !emailRegEx.test(input)) {
    
      return ['Địa chỉ email không hợp lệ'];
  }
  return [];
}

const phone = (input: string | null | undefined): Array<string> => {
  if (input === null 
    || input === undefined 
    || input.trim() === "" 
    || !phoneNumberRegex.test(input)) {
    
      return ['Số điện thoại không hợp lệ'];
  }
  return [];
}

const password = (input: string | null | undefined): Array<string> => {
  if (input === null 
    || input === undefined 
    || input.trim() === "" 
    || input.length < 8) {
    
      return ['Mật khẩu tối thiểu 8 ký tự'];
  }
  return [];
}

const confirmPassword = (input: string | null | undefined, match: string): Array<string> => {
  if (input === null 
    || input === undefined 
    || input.trim() === "" 
    || input !== match) {
    
      return ['Mật khẩu xác nhận không khớp'];
  }
  return [];
}

const CommonValidation = {
  requiredString,
  email,
  phone,
  password,
  confirmPassword,
};

export default CommonValidation;