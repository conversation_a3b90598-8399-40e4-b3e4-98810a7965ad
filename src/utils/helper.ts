/**
 * Timeout
 * @param ms
 * @returns milisecond
 */
export function delay(ms: number = 2000): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
}

/**
 * Pad number
 * @param num
 * @param size
 * @returns
 */
export function pad(num: number, size: number) {
  return String(num).padStart(size, '0');
}

/**
 * Upper first letter in word
 * @param text 
 * @returns 
 */
export function upperFirst(text: string){
 return String(text).charAt(0).toUpperCase() + String(text).slice(1);
}

/**
 * Generate uid
 * @returns
 */
export function generateUidFromTimestamp(): string {
  const timestamp = new Date().getTime();
  return timestamp.toString();
}
