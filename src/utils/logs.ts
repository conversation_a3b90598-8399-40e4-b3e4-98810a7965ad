import log from 'electron-log';
import path from 'path';
import fs from 'fs';
import { app } from 'electron';
import formatTimeLog from './format';

interface LogMessage {
  data: any[];
  level: string;
}

const isDev = process.env.NODE_ENV === 'development';
const logDir = path.join(app.getAppPath(), 'Logs');

/**
 * Get line and filename from stack trace
 * @returns
 */
const getCallerInfo = () => {
  const { stack } = new Error();
  if (!stack) return { file: 'unknown', line: '0' };

  const stackLines = stack.split('\n');

  // get stack line
  const callerLine = stackLines[3] || stackLines[2] || '';
  const match =
    callerLine.match(/\((.*):(\d+):\d+\)/) ||
    callerLine.match(/at (.*):(\d+):\d+/);

  if (match) {
    const filePath = match[1].split(path.sep);
    return {
      file: filePath[filePath.length - 1], // get file name
      line: match[2], // get line
    };
  }
  return { file: 'unknown', line: '0' };
};

const logFormat = '[{dateTime}] [{level}] [{processType}] {file}:{line} {text}';

const formatLog = (msg: LogMessage) => {
  const { file, line } = getCallerInfo();
  const dateTime = formatTimeLog();
  const level = msg.level || 'INFO';
  const processType = process.type || 'main';
  const formatted = logFormat
    .replace('{dateTime}', dateTime)
    .replace('{level}', level)
    .replace('{processType}', processType)
    .replace('{file}', file)
    .replace('{line}', line)
    .replace('{text}', msg.data.join(' '));
  return [formatted];
};

if (isDev) {
  log.transports.file.level = false;
  log.transports.console.level = 'debug';
  log.transports.console.format = formatLog;
} else {
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  // Disable console log for production
  log.transports.console.level = false;
  log.transports.file.level = 'debug';
  log.transports.file.format = formatLog;

  log.transports.file.resolvePathFn = () => {
    const date = new Date();
    const dateString = date.toISOString().split('T')[0];
    return path.join(logDir, `${dateString}.txt`);
  };
}

// stack trace when Error
log.hooks.push((message) => {
  if (message.data[0] instanceof Error) {
    message.data[0] = message.data[0].stack;
  }
  return message;
});

if (!isDev) {
  log.info('TopFace start run', {
    nodeVersion: process.version,
    electronVersion: process.versions.electron,
    platform: process.platform,
    logDir,
  });
}

export default log;
