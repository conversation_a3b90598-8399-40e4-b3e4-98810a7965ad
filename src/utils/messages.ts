/**
 * Centralized messages for application messages
 * @module messages
 */
export const messages = {
  validation: {
    requiredEmail: 'Điền email của bạn.',
    invalidEmail: 'Email không hợp lệ.',
    requiredPassword: 'Điền mật khẩu của bạn.',
    shortPassword: (minLength: number) => `Mật khẩu ít nhất phải ${minLength} ký tự.`,
    requiredFullname: 'Điền tên của bạn.',
    requiredPhone: 'Điền số điện thoại của bạn.',
    requiredAddress: 'Điền địa chỉ của bạn.',
    requiredPlan: 'Bạn chưa chọn gói đăng ký.',
    invalidPhone: 'Số điện thoại không hợp lệ.',
    requiredSearchQuery: 'Điền từ khóa tìm kiếm.',
    importFileEmpty: 'File danh sách trống',
    invalidFileType: 'Hỗ trợ định dạng file txt hoặc xlsx',
    invalidQtyColumns: 'File danh sách yêu cầu tối thiểu 1 cột và tối đa 6 cột',
    headerIsRequired: 'Tiêu đề cột được yêu cầu',
    passwordMismatch: 'Mật khẩu không khớp',
  },
  api: {
    registerSuccess: 'Đăng ký thành công! Vui lòng đăng nhập.',
    registerFailed: (message: string) => `Có lỗi xảy ra khi đăng ký: ${message}`,
    loginFailed: 'Đăng nhập thất bại. Vui lòng kiểm tra email và mật khẩu.',
    networkError: 'Có lỗi xảy ra khi kết nối đến máy chủ. Vui lòng thử lại sau.',
    unauthorized: 'Bạn không có quyền truy cập. Vui lòng đăng nhập lại.'
  },
  account: {
    notFound: 'Không tìm thấy thông tin tài khoản',
    alreadyExisted: 'Tài khoản này đã có trong danh sách',
    blocked: 'Tài khoản đã bị khóa',
    loginSuccess: 'Đăng nhập thành công',
    loginFailed: 'Đăng nhập thất bại',
  },
  group: {
    success: (groupUid: string) => `Đã gửi tin nhắn đến nhóm ${groupUid}`,
    privacy: (groupUid: string) => `Nhóm ${groupUid} thiết lập quyền riêng tư`,
    groupNotFound: 'Không tìm thấy thông tin nhóm.',
    groupApprovedMessage: 'Chỉ trưởng/phó cộng đồng/nhóm được gửi tin nhắn vào cộng đồng/nhóm',
  },
  sendMessage: {
    approvedByGroup: (groupUid: string) => `Tin nhắn gửi tới nhóm ${groupUid} đang chờ duyệt`,
  },
  general: {
    success: 'Yêu cầu được thực hiện thành công.',
    error: 'Đã xảy ra lỗi khi thực hiện yêu cầu.',
    loading: 'Đang xử lý...',
    resultNotFound: 'Không tìm thấy kết quả.'
  },
};

/**
 * Format a message with dynamic parameters
 * @param {string|Function} message - The message or message function
 * @param {...any} params - Parameters to insert into the message
 * @returns {string} - Formatted message
 */
export function formatMessage(message: string | Function, ...params: any[]): string {
  if (typeof message === 'function') {
    return message(...params);
  }
  return message;
}