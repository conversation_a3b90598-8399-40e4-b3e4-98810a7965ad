import registerAccountHandlers from './accountHandlers';
import registerFacebookHandlers from './facebookHandlers';
import categoryHandlers from './categoryHandlers';
import CampaignHandlers from './CampaignHandlers';
import registerElectronAPIHandlers from './electronAPIHandlers';
import registerAuthenticationHandlers from './authenticationHandlers';

export default function registerIPCHandlers(): void {
  categoryHandlers();
  registerAccountHandlers();
  registerFacebookHandlers();
  CampaignHandlers();
  registerElectronAPIHandlers();
  registerAuthenticationHandlers();
}
