import PostServices from '../services/PostServices';
import { IPostController } from '../interfaces/IPostController';
import { Post } from '../interfaces/Post';

export default class PostController implements IPostController {

  private postServices: PostServices;

  constructor(postServices: PostServices) {
    this.postServices = postServices;
  }

  async createPost(post: Post): Promise<{ success: boolean; message: string }> {
    try {
      this.postServices.createPost(post);
      return { success: true, message: 'Tạo bài viết thành công' };
    } catch {
      return { success: false, message: 'Lỗi khi tạo bài viết' };
    }
  }


  async getAllPosts(): Promise<Post[]> {
    try {
      return this.postServices.getAllPosts();
    } catch {
      return [];
    }
  }


  async deletePost(id: number): Promise<{ success: boolean; message: string }> {
    try {
      this.postServices.deletePost(id);
      return { success: true, message: '<PERSON><PERSON><PERSON> bà<PERSON> viết thành công' };
    } catch {
      return { success: false, message: 'Lỗi khi xóa bài viết' };
    }
  }


  async updatePost(
    id: number,
    post: Post,
  ): Promise<{ success: boolean; message: string }> {
    try {
      this.postServices.updatePost(id, post);
      return { success: true, message: 'Cập nhật bài viết thành công' };
    } catch {
      return { success: false, message: 'Lỗi khi cập nhật bài viết' };
    }
  }


  async getPostById(id: number): Promise<Post | undefined> {
    try {
      return this.postServices.getPostById(id);
    } catch {
      return undefined;
    }
  }
}
