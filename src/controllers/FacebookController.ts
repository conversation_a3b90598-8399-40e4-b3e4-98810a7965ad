import { Page } from 'puppeteer-core';
import AuthServices from '../services/facebook/AuthServices';
import GroupPostService from '../services/facebook/GroupPostService';
import GroupSearchServices from '../services/facebook/GroupSearchService';
import AccountFBServices from '../services/AccountFBServices';
import ImageServices from '../services/facebook/ImageServices';
import CampaignsServices from '../services/CampaignsServices';
import {
  FBContentPostRequest,
  FBSearchGroupsResponse,
} from '../types/FacebookGroup';
import { FBUser, FBUserLoginResponse } from '../types/FacebookUser';
import { IFBUser } from '../interfaces/IFacebookUser';
import {
  CampaignDetailsForRun,
  CampaignProgress,
  Group,
  User,
  UserGroup,
} from '../interfaces/Campaigns';
import log from '../utils/logs';
import { delay } from '../utils/helper';
import FacebookBrowser from '../services/facebook/FacebookBrowser';

export default class FacebookController {
  private authServices: AuthServices;

  private groupSearchServices: GroupSearchServices;

  private groupPostServices: GroupPostService;

  private accountFBServices: AccountFBServices;

  private campaignsServices: CampaignsServices;

  private imageServices: ImageServices;

  private stoppedCampaigns: Record<string, boolean> = {};

  private stoppall: boolean = false;

  constructor() {
    this.authServices = new AuthServices();
    this.groupPostServices = new GroupPostService();
    this.groupSearchServices = new GroupSearchServices();
    this.accountFBServices = new AccountFBServices();
    this.campaignsServices = new CampaignsServices();
    this.imageServices = new ImageServices();
  }

  /**
   * Handle login Facebook
   */
  async login(data: {
    username: string;
    password: string;
    profileId: string;
  }): Promise<FBUserLoginResponse> {
    const result = await this.authServices.login(data);
    return result;
  }

  /**
   * Get logged page
   * @param profileId
   */
  protected async getLoggedPage(profileId: string) {
    const { success, page, user, error } =
      await this.authServices.getLoggedPage(profileId);
    if (success === false || !page || !user) {
      return {
        success: false,
        error,
      };
    }

    return { success: true, page, user };
  }

  /**
   * Post to group
   * @param profileId
   * @param data
   */
  async postGroup(
    page: Page,
    user: FBUser,
    groupValue: string,
    content: FBContentPostRequest,
    isJoinGroup: boolean,
    campaignId: string,
    sendProgress: ({ message, campaignId, action }: CampaignProgress) => void,
  ) {
    sendProgress({
      message: `Đang truy cập tới nhóm ${groupValue}`,
      campaignId,
      action: 'running',
    });
    // GO TO GROUP - VALIDATE GROUP
    const result = await this.groupPostServices.goToGroup(page, groupValue);
    const { success, groupId, error } = result;

    if (error || !groupId) {
      sendProgress({
        message: error || `Đã xảy ra lỗi khi truy cập tới nhóm ${groupValue}`,
        campaignId,
        action: 'running',
      });
      log.error(error);
      return { success: false, error };
    }

    // START POSTING
    const response = await this.groupPostServices.postToGroup(
      page,
      groupId,
      user,
      content,
    );
    if (response.error) {
      sendProgress({ message: response.error, campaignId, action: 'running' });
      log.error(response.error);
      return { success: false, error: response.error };
    }

    // GET POST ID AFTER SUBMIT
    const res = await this.groupPostServices.getPostIdAfterSubmit(
      page,
      groupId,
      user,
    );
    if (res.success && res.groupPost) {
      return { success: true, groupPost: res.groupPost };
    }

    return {
      success: false,
      error: `Không thể lấy ID bài đăng trong nhóm ${groupValue}`,
    };
  }

  /**
   * Search groups
   * @param text
   * @returns
   */
  async searchGroups(
    profileId: string,
    text: string,
    targetGroup: number,
  ): Promise<FBSearchGroupsResponse> {
    const result = await this.getLoggedPage(profileId);
    if (result.success === false || !result.page) {
      return result;
    }

    const { page } = result;
    const response = this.groupSearchServices.searchGroups(
      page,
      text,
      targetGroup,
    );
    return response;
  }

  private async getAccountsForRun(
    campaign: CampaignDetailsForRun,
    ListUser: IFBUser[],
    sendProgress: ({ message, campaignId, action }: CampaignProgress) => void,
  ): Promise<{
    finalUserList: IFBUser[];
    profilePostCount: Record<string, number>;
  }> {
    const campaignId = campaign.Campaign.id;
    let finalUserList: IFBUser[] = [];
    const profilePostCount: Record<string, number> = {};

    for (const u of ListUser) {
      const userInCampaign = campaign.User.find(
        (i) => i.profileId === u.profileId,
      );
      profilePostCount[u.profileId] = userInCampaign?.numbersend ?? 0;
      this.accountFBServices.updateUser({ ...u, status: 'active' });
    }

    finalUserList = ListUser;
    sendProgress({
      message: 'Lấy danh sách tài khoản',
      campaignId,
      action: 'running',
    });

    return { finalUserList, profilePostCount };
  }

  /**
   * Search groups
   * @param list_username
   * @param id
   */
  async start(
    ListUser: IFBUser[],
    campaignId: string,
    sendProgress: ({ message, campaignId, action }: CampaignProgress) => void,
  ) {
    try {
      this.stoppedCampaigns[campaignId] = false;
      const campaign = this.campaignsServices.getDataForRunCampaign(campaignId);

      if (!campaign) {
        sendProgress({
          message: `Không tìm thấy cấu hình hoặc data cho chiến dịch`,
          campaignId,
          action: 'stopped',
        });
        this.stoppedCampaigns[campaignId] = true;
        return {
          success: false,
          message: `Không tìm thấy cấu hình hoặc data cho chiến dịch ${campaignId}`,
        };
      }
      sendProgress({
        message: 'Chiến dịch chuẩn bị khởi chạy',
        campaignId,
        action: 'running',
      });

      const { finalUserList, profilePostCount } = await this.getAccountsForRun(
        campaign,
        ListUser,
        sendProgress,
      );

      const isStatusUpdated = this.campaignsServices.updateCampaignStatus(
        campaignId,
        'running',
      );
      if (!isStatusUpdated) {
        log.error(
          `Lỗi cập nhật trạng thái Running cho chiến dịch ${campaignId}`,
        );
        this.stoppedCampaigns[campaignId] = true;
      }

      const {
        delay: campaignDelay,
        max_post,
        is_joingroup,
      } = campaign.CampaignConfiguration;
      const { groupIds, imagePaths } = campaign;

      const baseContent: FBContentPostRequest = {
        message: campaign.Campaign?.message,
        filePaths: imagePaths.length > 0 ? imagePaths : [],
      };

      if (finalUserList.length === 0) {
        const updatedCampaign = this.campaignsServices.updateCampaignStatus(
          campaignId,
          'stopped',
        );
        if (!updatedCampaign) {
          log.error(
            `Lỗi cập nhật trạng thái Stopped cho chiến dịch ${campaignId}`,
          );
        }
        sendProgress({
          message: `Không có tài khoản nào để chạy chiến dịch`,
          campaignId,
          action: 'stopped',
        });
        return {
          success: false,
          message: `Không có tài khoản nào để chạy chiến dịch ${campaignId}`,
        };
      }

      const profileSessions = await this.initProfileSessions(
        finalUserList,
        campaignId,
      );

      await this.runWhileLoopCampaign(
        campaignId,
        finalUserList,
        profileSessions,
        profilePostCount,
        groupIds,
        baseContent,
        max_post,
        campaignDelay,
        is_joingroup,
        sendProgress,
      );

      // Đóng tất cả page
      await Promise.all(
        Object.values(profileSessions).map((s) => s.page.close()),
      );
      const isUpdatedCamp = this.campaignsServices.updateCampaignStatus(
        campaignId,
        'done',
      );
      if (!isUpdatedCamp) {
        log.error(`Lỗi cập nhật trạng thái Done cho chiến dịch ${campaignId}`);
      }
      const updatedUsers = finalUserList.map((User) => {
        const isUpdated = this.accountFBServices.updateUser({
          ...User,
          status: 'active',
        });
        if (isUpdated) {
          return User.username;
        }
      });

      if (updatedUsers.length === finalUserList.length) {
        sendProgress({
          message: 'Chiến dịch đã hoàn thành',
          campaignId,
          action: 'done',
        });
        return { success: true, message: 'Chiến dịch đã hoàn thành' };
      }

      return {
        success: false,
        message: 'Lỗi cập nhật trạng thái cho một số tài khoản',
      };
    } catch (error) {
      return {
        success: false,
        message: error?.toString() || 'Lỗi không xác định',
      };
    }
  }

  private async runWhileLoopCampaign(
    campaignId: string,
    finalUserList: IFBUser[],
    profileSessions: Record<
      string,
      { page: Page; user: IFBUser; name: FBUser }
    >,
    profilePostCount: Record<string, number>,
    groupIds: string[],
    baseContent: FBContentPostRequest,
    max_post: number,
    campaignDelay: number,
    is_joingroup: boolean,
    sendProgress: ({ message, campaignId, action }: CampaignProgress) => void,
  ) {
    const profileIds = Object.keys(profileSessions);

    const groupSuccessStatus: Record<string, boolean> = {};
    const groupTriedBy: Record<string, Set<string>> = {};
    const groupLocked: Set<string> = new Set();

    groupIds.forEach((gid) => {
      groupSuccessStatus[gid] = false;
      groupTriedBy[gid] = new Set();
      const usergroup = this.campaignsServices.getUserGroup(campaignId, gid);
      usergroup?.forEach((user) => {
        groupTriedBy[gid].add(user.profileId);
      });
    });

    while (true) {
      if (this.stoppedCampaigns[campaignId]) {
        this.stoppedCampaigns[campaignId] = false;

        await Promise.all(
          Object.values(profileSessions).map((s) => s.page.close()),
        );
        const updatedCampaign = this.campaignsServices.updateCampaignStatus(
          campaignId,
          'stopped',
        );
        if (!updatedCampaign) {
          log.error(
            `Lỗi cập nhật trạng thái Stopped cho chiến dịch ${campaignId}`,
          );
        }

        finalUserList.map((User) => {
          const isUpdated = this.accountFBServices.updateUser({
            ...User,
            status: 'active',
          });
          if (isUpdated) {
            return User.username;
          }
        });
        sendProgress({
          message: 'Chiến dịch đã dừng',
          campaignId,
          action: 'stopped',
        });
        return { success: true, message: 'Chiến dịch đã dừng' };
      }

      const availableProfiles = profileIds.filter(
        (pid) => profilePostCount[pid] < max_post,
      );
      const remainingGroups = groupIds.filter(
        (gid) =>
          !groupSuccessStatus[gid] &&
          availableProfiles.some((pid) => !groupTriedBy[gid].has(pid)),
      );

      if (availableProfiles.length === 0 || remainingGroups.length === 0) break;

      const tasks: Promise<void>[] = [];

      for (const profileId of availableProfiles) {
        const session = profileSessions[profileId];
        if (!session) continue;

        // Tìm nhóm mà tài khoản này chưa thử và nhóm chưa thành công
        const groupId = groupIds.find(
          (gid) =>
            !groupSuccessStatus[gid] &&
            !groupTriedBy[gid].has(profileId) &&
            !groupLocked.has(gid),
        );
        sendProgress({ message: `Nhóm đang đợi xử lý ${groupId}`, campaignId });

        if (!groupId) continue;

        groupTriedBy[groupId].add(profileId);
        groupLocked.add(groupId);

        const task = (async () => {
          try {
            log.info(
              `Đang xử lý nhóm ${groupId} với tài khoản ${session.name}`,
            );
            const response = await this.postGroup(
              session.page,
              session.name,
              groupId,
              baseContent,
              is_joingroup,
              campaignId,
              sendProgress,
            );

            if (response.success && response.groupPost) {
              profilePostCount[profileId] += 1;
              groupSuccessStatus[groupId] = true;

              const updatedUser: User = {
                campaign_id: Number(campaignId),
                profileId,
                numbersend: profilePostCount[profileId],
              };
              const isUpdatedUser =
                this.campaignsServices.update_listUser(updatedUser);
              if (!isUpdatedUser) {
                log.error(
                  `Lỗi cập nhật số lượt chạy cho tài khoản ${profileId}`,
                );
              }

              const updatedGroup: Group = {
                campaign_id: campaignId,
                groupID: groupId,
                status: response.groupPost.status,
                postId: response.groupPost.postId,
              };

              const isUpdatedGroup =
                this.campaignsServices.updateListGroupStatus(updatedGroup);
              if (!isUpdatedGroup) {
                log.error(
                  `Lỗi cập nhật trạng thái cho nhóm ${groupId} trong chiến dịch ${campaignId}`,
                );
              }

              const userGroup: UserGroup = {
                groupId: groupId,
                userpost: session.user.username,
                profileId,
                status: 'done',
              };
              const isUpdatedUserGroup = this.campaignsServices.inserUserGroup(
                campaignId,
                userGroup,
              );
              if (!isUpdatedUserGroup) {
                log.error(
                  `Lỗi cập nhật tài khoản đăng bài vào nhóm ${groupId} trong chiến dịch ${campaignId}`,
                );
              }

              const { postId, status } = response.groupPost;
              let mess = `Đăng bài vào nhóm thành công - ID bài đăng: ${postId}`;
              if (status === 'pending') {
                mess = `Đăng bài vào nhóm thành công - Bài đăng đang chờ duyệt - ID bài đăng: ${postId}`;
              }
              sendProgress({ message: mess, campaignId, action: 'running' });
            } else {
              const userGroup: UserGroup = {
                groupId: groupId,
                userpost: session.user.username,
                profileId,
                status: 'false',
              };
              const isUpdatedUserGroup = this.campaignsServices.inserUserGroup(
                campaignId,
                userGroup,
              );
              if (!isUpdatedUserGroup) {
                log.error(
                  `Lỗi cập nhật tài khoản đăng bài vào nhóm ${groupId} trong chiến dịch ${campaignId}`,
                );
              }
              const mess =
                response.error || `Không thể đăng bài vào nhóm ${groupId}`;
              sendProgress({ message: mess, campaignId });
            }
          } catch (err) {
            console.warn(`❌ Lỗi khi ${profileId} gửi nhóm ${groupId}`, err);
          } finally {
            groupLocked.delete(groupId);
          }
        })();

        tasks.push(task);
      }

      await Promise.all(tasks);

      const stillAvailable = profileIds.filter(
        (pid) => profilePostCount[pid] < max_post,
      );
      const stillGroups = groupIds.filter(
        (gid) =>
          !groupSuccessStatus[gid] &&
          stillAvailable.some((pid) => !groupTriedBy[gid].has(pid)),
      );

      if (stillGroups.length > 0 && stillAvailable.length > 0) {
        await delay(campaignDelay * 1000);
      } else {
        break;
      }
    }

    // cập nhật trạng thái nhóm chưa gửi được bài
    for (const groupId of groupIds) {
      if (!groupSuccessStatus[groupId]) {
        const updatedGroup: Group = {
          campaign_id: campaignId,
          groupID: groupId,
          status: 'false',
          postId: '',
        };
        const isUpdatedGroup =
          this.campaignsServices.updateListGroupStatus(updatedGroup);
        if (!isUpdatedGroup) {
          log.error(
            `Lỗi cập nhật trạng thái cho nhóm ${groupId} trong chiến dịch ${campaignId}`,
          );
        }
      }
    }
  }

  /**
   * Initialize profile sessions
   * @param finalUserList
   * @param campaignId
   * @returns
   */
  private async initProfileSessions(
    finalUserList: IFBUser[],
    campaignId: string,
  ) {
    const profileSessions: Record<
      string,
      { page: Page; user: IFBUser; name: any }
    > = {};
    const concurrencyLimit = 3; // Set limit for init browsers

    // Hàm helper xử lý từng user
    const processUser = async (user: IFBUser) => {
      try {
        const isUpdated = this.accountFBServices.updateUser({
          ...user,
          status: 'running',
        });
        if (!isUpdated) {
          log.error(
            `Lỗi cập nhật trạng thái Running cho tài khoản ${user.username}`,
            { profileId: user.profileId },
          );
        }

        const result = await this.getLoggedPage(user.profileId);
        if (result.success && result.page && result.user) {
          profileSessions[user.profileId] = {
            page: result.page,
            user,
            name: result.user,
          };
        } else {
          log.warn(`Không thể truy cập profile page của ${user.username}`, {
            profileId: user.profileId,
            error: result.error,
          });
        }
      } catch (error) {
        log.error(`Lỗi xử lý user ${user.username}`, {
          profileId: user.profileId,
          error,
        });
      }
    };

    // Chia list thành batch và xử lý tuần tự
    for (let i = 0; i < finalUserList.length; i += concurrencyLimit) {
      const batch = finalUserList.slice(i, i + concurrencyLimit);
      await Promise.all(batch.map(processUser));
    }

    return profileSessions;
  }

  stopbycampaign(CampaignId: string) {
    this.stoppedCampaigns[CampaignId] = true;
    return { success: true, message: 'Chiến dịch đã dừng thành công' };
  }

  stopAll() {
    this.stoppall = true;

    const allCampaigns = this.campaignsServices.getAllCampaigns();
    const runningCampaigns = allCampaigns.filter((c) => c.status === 'running');
    if (runningCampaigns.length === 0) {
      return { success: true, message: 'Không có chiến dịch nào đang chạy' };
    }

    const result = runningCampaigns.map((campaign) => {
      this.stoppedCampaigns[campaign.id] = true;
      const isUpdated = this.campaignsServices.updateCampaignStatus(
        campaign.id,
        'stopped',
      );
      if (isUpdated) {
        return campaign.id;
      }
    });
    if (result.length === runningCampaigns.length) {
      this.stoppall = false;
      return { success: true, message: 'Tất cả chiến dịch đã dừng thành công' };
    }

    log.error(`Lỗi khi dừng một số chiến dịch: ${result.join(', ')}`);
    return { success: false, message: 'Lỗi khi dừng một số chiến dịch' };
  }

  // tao user để gửi bài viết
  createUserpost(ListUser: IFBUser[], campaignId: string) {
    try {
      const campaign = this.campaignsServices.getDataForRunCampaign(campaignId);
      if (!campaign) {
        return {
          success: false,
          message: `Không tìm thấy cấu hình hoặc data cho chiến dịch ${campaignId}`,
        };
      }

      const requestedProfileIds = new Set(ListUser.map((u) => u.profileId));
      const existingProfileIds = new Set(campaign.User.map((u) => u.profileId));
      const newUsers = ListUser.filter(
        (u) => !existingProfileIds.has(u.profileId),
      );

      newUsers.forEach((user) => {
        const id = this.campaignsServices.create_listUser(
          campaignId,
          user.profileId,
        );
        if (!id) {
          log.error(
            `Lỗi thêm tài khoản profileId ${user.profileId} cho chiến dịch ${campaignId}`,
          );
        }
      });
      return { success: true, message: '' };
    } catch (error) {
      return { success: false, message: `Lỗi thêm tài khoản` };
    }
  }

  //copy anh
  async copyProfileImage(sourceImagePath: string[]) {
    const source = await this.imageServices.copyProfileImage(sourceImagePath);
    return { success: true, source: source };
  }
}
