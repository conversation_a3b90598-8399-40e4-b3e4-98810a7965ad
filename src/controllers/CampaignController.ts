import { ICampaignsController } from '../interfaces/ICampaignsController';
import { Campaign, CampaignConfigurationRequest, CampaignDetails, CampaignRequest, GroupRequest, ImageRequest, UserGroup } from '../interfaces/Campaigns';
import CampaignsServices from '../services/CampaignsServices';
import CampaignConfigServices from '../services/CampaignConfigServices';
import CampaignGroupServices from '../services/CampaignGroupServices';
import CampaignImageServices from '../services/CampaignImageServices';


export default class CampaignsController implements ICampaignsController {
  private campaignsServices: CampaignsServices;

  private configServices: CampaignConfigServices;

  private groupServices: CampaignGroupServices;

  private imageServices: CampaignImageServices;

  constructor(
    campaignsServices: CampaignsServices,
    configServices: CampaignConfigServices,
    groupServices: CampaignGroupServices,
    imageServices: CampaignImageServices
  ) {
    this.campaignsServices = campaignsServices;
    this.configServices = configServices;
    this.groupServices = groupServices;
    this.imageServices = imageServices;
  }

  createCampaign(data: CampaignRequest): { success: boolean; data?: Campaign; error?: string } {
    const campaignId = this.campaignsServices.createCampaign(data);
    if (!campaignId) {
      return { success: false, error: 'Tạo chiến dịch thất bại.' };
    }

    const campaign = this.campaignsServices.getCampaign(campaignId.toString());
    if (!campaign) {
      return { success: false, error: 'Tạo chiến dịch thất bại.' };
    }

    return { success: true, data: campaign }
  }

  saveCampaignConfig(data: CampaignConfigurationRequest): { success: boolean; error?: string } {

    const {campaignId} = data;
    const campaign = this.campaignsServices.getCampaign(campaignId);

    if (!campaign) {
      return { success: false, error: `Không tìm thấy chiến dịch id ${campaignId}` };
    }

    const existed = this.configServices.findByCampaignId(campaignId);
    if (!existed) {
      const configId = this.configServices.createConfig(campaignId, data);
      if (!configId) {
        return { success: false, error: 'Thiết lập cấu hình chiến dịch thất bại' };
      }

      return { success: true }
    }

    const updated = this.configServices.updateConfig(campaignId, data);
    if (!updated) {
      return { success: false, error: 'Thiết lập cấu hình chiến dịch thất bại' };
    }

    return { success: true }
  }

  saveCampaignGroup(data: GroupRequest): { success: boolean; error?: string } {

    const {campaignId} = data;
    const campaign = this.campaignsServices.getCampaign(campaignId);
    if (!campaign) {
      return { success: false, error: `Không tìm thấy chiến dịch id ${campaignId}` };
    }

    if (data.groupIds && data.groupIds.length === 0){
      return { success: false, error: 'Danh sách nhóm trống' };
    }

    const existed = this.groupServices.getAllByCampaignId(campaignId);
    if (existed.length > 0 ) {
      const deleted = this.groupServices.deleteGroupByCampaignId(campaignId);
      if (!deleted) {
        return { success: false, error: 'Đã xảy ra lỗi khi lưu nhóm' };
      }
    }

    const group = this.groupServices.createGroupList(data);
    if (!group) {
      return { success: false, error: 'Tạo danh sách nhóm thất bại' };
    }

    return { success: true };
  }

  saveCampaignImage(data: ImageRequest): { success: boolean; error?: string } {

    const {campaignId} = data;
    const campaign = this.campaignsServices.getCampaign(campaignId);
    if (!campaign) {
      return { success: false, error: `Không tìm thấy chiến dịch id ${campaignId}` };
    }

    const existed = this.imageServices.getAllByCampaignId(campaignId);
    if (existed.length > 0 ) {
      const deleted = this.imageServices.deleteImageByCampaignId(campaignId);
      if (!deleted) {
        return { success: false, error: 'Đã xảy ra lỗi khi lưu ảnh' };
      }
    }

    const image = this.imageServices.createImageList(data);
    if (!image) {
      return { success: false, error: 'Lưu hình ảnh cho chiến dịch thất bại' };
    }

    return { success: true };
  }

  getAllCampaigns(type?: string): Campaign[] {
    return this.campaignsServices.getAllCampaigns(type);
  }

  getCampaignDetails(id: string): CampaignDetails | null  {
    const campaign = this.campaignsServices.getCampaign(id);
    if (!campaign) {
      return null;
    }

    const config = this.configServices.findByCampaignId(id);
    const groups = this.groupServices.getAllByCampaignId(id);
    const images = this.imageServices.getAllByCampaignId(id);
    const users = this.campaignsServices.getListUserByCampaignId(id);

    return {
      Campaign: campaign,
      CampaignConfiguration: config || undefined,
      Group: groups,
      Image: images,
      User: users,
    };
  }

  updateCampaign(data: CampaignRequest): { success: boolean; data?: Campaign; error?: string } {
    if (!data.id) {
      return { success: false, error: 'Dữ liệu không hợp lệ' };
    }

    const campaign = this.campaignsServices.getCampaign(data.id);
    if (!campaign) {
      return { success: false, error: 'Không tìm thấy chiến dịch' };
    }

    const isUpdated = this.campaignsServices.updateCampaign(data.id, data);
    if (!isUpdated) {
      return { success: false, error: `Cập nhật chiến dịch Id ${data.id} thất bại` };
    }

    return { success: true, data: campaign }
  }

  deleteCampaign(id: number): { success: boolean; message: string } {
    const campaign = this.campaignsServices.getCampaign(id.toString());
    if (!campaign) {
      return { success: false, message: `Không tìm thấy chiến dịch Id ${id}` };
    }

    const isDeleted = this.campaignsServices.deleteCampaign(id);
    if (!isDeleted) {
      return { success: false, message: `Xóa chiến dịch Id ${id} thất bại` };
    }

    return { success: true, message: 'Xóa thành công.' };
  }

  getCampaign(id: string): { success: boolean; data? : Campaign } {
    const campaign = this.campaignsServices.getCampaign(id)
    if (campaign) {
      return { success: true, data: campaign };
    }
    return {success: false}
  }

  bulkDeleteCampaigns(ids: string[]): boolean {
    if (ids.length === 0) {
      return false;
    }

    const isDeleted = this.campaignsServices.bulkDeleteCampaigns(ids);
    if (isDeleted) {
      return true;
    }

    return false;
  }


  getuserpost(groupID: string) : UserGroup{
    return this.campaignsServices.getuserpost(groupID);
  }
}
