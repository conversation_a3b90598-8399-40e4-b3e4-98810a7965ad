import CategoryServices from '../services/CategoryServices';
import { ICategoryController } from '../interfaces/ICategoryController';
import { Category } from '../interfaces/Categorys';

export default class CategoryController implements ICategoryController {

  private categoryServices: CategoryServices;

  constructor(categoryServices: CategoryServices) {
    this.categoryServices = categoryServices;
  }

  createCategory(name: string, description: string, resourceType: string):{ success: boolean, data?: Category } {
    try {
      const id = this.categoryServices.createCategory(name, description, resourceType);
      if (id) {
        const category = this.categoryServices.getCategory(id);
        if (category) {
          return { success: true, data: category };
        }
      }
      return { success: false };
    } catch {
      return { success: false };
    }
  }


  getAllCategorys(resourceType?: string): Category[] {
    return this.categoryServices.getAllCategorys(resourceType);
  }

  deleteCategory(id: number|string): { success: boolean } {
    const result = this.categoryServices.deleteCategory(id);
    if (result) {
      return { success: true };
    }
    return { success: false };
  }
}
