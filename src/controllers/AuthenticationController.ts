/**
 * Authentication Controller
 * Handles authentication business logic and coordinates between services
 * Follows the existing controller pattern in the app
 */

import log from '../utils/logs';
import ApiConfig from '../config/ApiConfig';
import authenticationService from '../services/AuthenticationService';
import tokenStorageService from '../services/TokenStorageService';
import {
  IAuthenticationController,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  LogoutResponse,
  RefreshTokenResponse,
  SyncSessionResponse,
  User,
  TokenStorage,
} from '../interfaces/IAuthentication';

export class AuthenticationController implements IAuthenticationController {
  constructor(
    private authService = authenticationService,
    private tokenStorage = tokenStorageService
  ) {}

  /**
   * Handle user login
   */
  async login(request: LoginRequest): Promise<LoginResponse> {
    try {
      log.info('AuthenticationController: Processing login request', { email: request.email });

      // Validate input
      if (!request.email || !request.password) {
        return {
          success: false,
          error: 'Email and password are required',
        };
      }

      // Attempt login
      const loginResult = await this.authService.login(request);

      if (loginResult.success && loginResult.data) {
        // Store tokens securely
        const tokenData: TokenStorage = {
          token: loginResult.data.token,
          sessionId: loginResult.data.sessionId,
          expiresAt: loginResult.data.expiresAt,
          user: loginResult.data.user,
        };

        const storageSuccess = await this.tokenStorage.saveTokens(tokenData);
        if (!storageSuccess) {
          log.warn('Failed to save tokens to storage, but login was successful');
        }

        log.info('Login successful', { userId: loginResult.data.user.id });
      }

      return loginResult;
    } catch (error: any) {
      log.error('AuthenticationController: Login error', error);
      return {
        success: false,
        error: 'An unexpected error occurred during login',
      };
    }
  }

  /**
   * Handle user registration
   */
  async register(request: RegisterRequest): Promise<RegisterResponse> {
    try {
      log.info('AuthenticationController: Processing registration request', { email: request.email });

      // Validate input
      const validationError = this.validateRegistrationRequest(request);
      if (validationError) {
        return {
          success: false,
          error: validationError,
        };
      }

      // Attempt registration
      const registerResult = await this.authService.register(request);

      if (registerResult.success && registerResult.data) {
        log.info('Registration successful', { userId: registerResult.data.user.id });

        // If registration includes authentication tokens, store them
        if (registerResult.data.token && registerResult.data.sessionId) {
          const tokenData: TokenStorage = {
            token: registerResult.data.token,
            sessionId: registerResult.data.sessionId,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Default 24h
            user: registerResult.data.user,
          };

          await this.tokenStorage.saveTokens(tokenData);
        }
      }

      return registerResult;
    } catch (error: any) {
      log.error('AuthenticationController: Registration error', error);
      return {
        success: false,
        error: 'An unexpected error occurred during registration',
      };
    }
  }

  /**
   * Handle user logout
   */
  async logout(): Promise<LogoutResponse> {
    try {
      log.info('AuthenticationController: Processing logout request');

      // Attempt server logout
      const logoutResult = await this.authService.logout();

      // Clear local storage regardless of server response
      await this.tokenStorage.clearTokens();

      log.info('Logout completed');
      return logoutResult;
    } catch (error: any) {
      log.error('AuthenticationController: Logout error', error);
      
      // Still clear local storage on error
      await this.tokenStorage.clearTokens();
      
      return {
        success: true,
        message: 'Logged out locally',
      };
    }
  }

  /**
   * Handle token refresh
   */
  async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      log.info('AuthenticationController: Processing token refresh');

      // Check if we have stored tokens
      const storedTokens = await this.tokenStorage.loadTokens();
      if (!storedTokens) {
        return {
          success: false,
          error: 'No stored authentication data found',
        };
      }

      // Attempt token refresh
      const refreshResult = await this.authService.refreshToken();

      if (refreshResult.success && refreshResult.data) {
        // Update stored tokens
        await this.tokenStorage.updateToken(
          refreshResult.data.token,
          refreshResult.data.expiresAt
        );

        log.info('Token refresh successful');
      }

      return refreshResult;
    } catch (error: any) {
      log.error('AuthenticationController: Token refresh error', error);
      return {
        success: false,
        error: 'An unexpected error occurred during token refresh',
      };
    }
  }

  /**
   * Handle session sync
   */
  async syncSession(sessionId: string): Promise<SyncSessionResponse> {
    try {
      log.info('AuthenticationController: Processing session sync', { sessionId });

      if (!sessionId) {
        return {
          success: false,
          error: 'Session ID is required',
        };
      }

      // Attempt session sync
      const syncResult = await this.authService.syncSession({ sessionId });

      if (syncResult.success && syncResult.data) {
        // Update stored tokens
        const tokenData: TokenStorage = {
          token: syncResult.data.token,
          sessionId: syncResult.data.sessionId,
          expiresAt: syncResult.data.expiresAt,
          user: syncResult.data.user,
        };

        await this.tokenStorage.saveTokens(tokenData);
        log.info('Session sync successful');
      }

      return syncResult;
    } catch (error: any) {
      log.error('AuthenticationController: Session sync error', error);
      return {
        success: false,
        error: 'An unexpected error occurred during session sync',
      };
    }
  }

  /**
   * Get current authenticated user
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const storedTokens = await this.tokenStorage.loadTokens();
      return storedTokens?.user || null;
    } catch (error: any) {
      log.error('AuthenticationController: Get current user error', error);
      return null;
    }
  }

  /**
   * Check authentication status
   */
  async checkAuthStatus(): Promise<{ isAuthenticated: boolean; user: User | null }> {
    try {
      const storedTokens = await this.tokenStorage.loadTokens();
      const isAuthenticated = storedTokens !== null;

      if (isAuthenticated && storedTokens) {
        // Update ApiConfig with stored tokens
        ApiConfig.bearerToken = storedTokens.token;
        ApiConfig.sessionId = storedTokens.sessionId;
      }

      return {
        isAuthenticated,
        user: storedTokens?.user || null,
      };
    } catch (error: any) {
      log.error('AuthenticationController: Check auth status error', error);
      return {
        isAuthenticated: false,
        user: null,
      };
    }
  }

  /**
   * Initialize authentication on app startup
   */
  async initializeAuth(): Promise<void> {
    try {
      log.info('AuthenticationController: Initializing authentication');

      const authStatus = await this.checkAuthStatus();
      if (authStatus.isAuthenticated) {
        log.info('User is authenticated', { userId: authStatus.user?.id });
      } else {
        log.info('User is not authenticated');
      }
    } catch (error: any) {
      log.error('AuthenticationController: Initialize auth error', error);
    }
  }

  /**
   * Validate registration request
   */
  private validateRegistrationRequest(request: RegisterRequest): string | null {
    if (!request.email) return 'Email is required';
    if (!request.password) return 'Password is required';
    if (!request.password_confirmation) return 'Password confirmation is required';
    if (request.password !== request.password_confirmation) return 'Passwords do not match';
    if (!request.phone) return 'Phone number is required';
    if (!request.full_name) return 'Full name is required';
    if (!request.app_code) return 'App code is required';

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(request.email)) return 'Invalid email format';

    // Basic password validation
    if (request.password.length < 6) return 'Password must be at least 6 characters';

    return null;
  }
}

// Export singleton instance
export const authenticationController = new AuthenticationController();
export default authenticationController;
