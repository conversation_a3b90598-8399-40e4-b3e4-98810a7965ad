import { Page } from 'puppeteer-core';
import AccountFBServices from '../services/AccountFBServices';
import { IAccountFBController } from '../interfaces/IAccountFBController';
import { IFBUser, IFBUserRequest, IFBUserResponse } from '../interfaces/IFacebookUser';
import CategoryServices from '../services/CategoryServices';
import log from '../utils/logs';
import AuthServices from '../services/facebook/AuthServices';
import { messages } from '../utils/messages';

export default class AccountFBController implements IAccountFBController {

  private accountFBServices: AccountFBServices;

  private categoryServices: CategoryServices;

  private authServices: AuthServices;

  constructor(accountFBServices: AccountFBServices, categoryServices: CategoryServices,  authServices: AuthServices) {
    this.accountFBServices = accountFBServices;
    this.categoryServices = categoryServices;
    this.authServices = authServices;
  }
  
  findByUserId(userId: string): { success: boolean; data? : IFBUser } {
    const user = this.accountFBServices.findByUserId(userId);
    if (user) {
      return { success: true, data: user}
    }
    return { success: false }
  }

  getUser(id: string): { success: boolean; data? : IFBUserResponse } {
    const user = this.accountFBServices.getUser(id);
    if (user) {
      return { success: true, data: user };
    }
    return {success: false}
  }


  createUser(data: IFBUserRequest) : { success: boolean; error?: string; data? : IFBUserResponse } {
    try {
      if (data.categoryId) {
        const category = this.categoryServices.getCategory(data.categoryId);
        if (!category) {
          log.error('Không tìm thấy danh mục ID: ', data.categoryId);
          return { success: false };
        }
      }

      // check existed by userId
      const {userId} = data;
      if (userId && userId !== '') {
        const existed = this.accountFBServices.findByUserId(userId);
        if (existed) {
          return { success: false, error: messages.account.alreadyExisted };
        }
      }

      const id = this.accountFBServices.createUser(data);
      if (id) {
        const user = this.accountFBServices.getUser(id);
        if (user) {
          return { success: true, data: user };
        }
      }
      return { success: false };
    } catch {
      log.error('Tài khoản đã tồn tại hoặc lỗi DB');
      return { success: false };
    }
  }


  getAllUsers(search?: string): IFBUserResponse[] {
    return this.accountFBServices.getAllUsers(search);
  }


  deleteUser(id: string) {
    const result = this.accountFBServices.deleteUser(id);
    if (result) {
      return { success: true };
    }

    return { success: false };
  }


  updateUser(data : IFBUser): { success: boolean; error?: string; data? : IFBUserResponse } {
    // check existed by userId
    const {userId} = data;
    if (userId && userId !== '') {
      const existed = this.accountFBServices.findByUserId(userId);
      if (existed && existed.id !== data.id) {
        return { success: false, error: messages.account.alreadyExisted };
      }
    }

    const result = this.accountFBServices.updateUser(data);
    if (result) {
      const user = this.accountFBServices.getUser(data.id);
      if (user) {
        return { success: true, data: user };
      }
    }

    return { success: false };
  }

  async launchProfilePage(id: string|number): Promise<{ success: boolean; page?: Page; }> {
    const user = this.accountFBServices.getUser(id);
    if (user) {
      const result = await this.authServices.launchProfilePage(user.profileId);
      if (result.success && result.page) {
        return { success: true, page: result.page }
      }
    }

    return { success: false }
  }

  async getUserByStatus(status: string): Promise<IFBUserResponse[]> {
    const users = this.accountFBServices.getUserByStatus(status);
    if (users && users.length > 0) {
      return users;
    }
    return [];
  }

  bulkDeleteUsers(ids: string[]): boolean {
    if (ids && ids.length > 0) {
      const result = this.accountFBServices.bulkDeleteUsers(ids);
      if (result) {
        return true;
      }
    }
    return false;
  }
}
