// src/main/database/db.ts
import Database from 'better-sqlite3';
import type { Database as DatabaseType } from 'better-sqlite3';
import fs from 'fs';
import path from 'path';

interface IDatabaseManager {
  getDb(): DatabaseType
}

class DatabaseManager implements IDatabaseManager {

  private static instance: IDatabaseManager;

  private db: DatabaseType;

  public databasePath: string = path.resolve(__dirname, '../../data');


  public databaseFile: string = path.join(this.databasePath, 'app.db');


  constructor() {
    if (!fs.existsSync(this.databasePath)) {
      fs.mkdirSync(this.databasePath, { recursive: true });
    }

    this.db = new Database(this.databaseFile);
    this.init();
  }

  public static getInstance(): IDatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance;
  }

  public getDb(): DatabaseType {
    return this.db;
  }

  private init(): void {
    // Bảng CATEGORIES (danh mục)
    this.db.prepare(
      `CREATE TABLE IF NOT EXISTS categories (
        category_id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_name TEXT NOT NULL,
        category_description TEXT,
        category_resourceType VARCHAR(100)
      )`
    ).run();

    // Bảng USERS
    this.db.prepare(
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(100),
        password VARCHAR(20),
        profileId VARCHAR(50),
        userId VARCHAR(100),
        status VARCHAR(255), -- trạng thái active|inactive
        categoryId INTEGER,
        FOREIGN KEY (categoryId) REFERENCES categories(category_id)
      )`
    ).run();

    // Tạo bảng posts
    this.db.prepare(
      `CREATE TABLE IF NOT EXISTS posts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        message TEXT NOT NULL,
        image TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )`
    ).run();

    // Tạo bảng campaigns
    this.db.prepare(
      `CREATE TABLE IF NOT EXISTS campaigns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        name TEXT NOT NULL,
        message TEXT NOT NULL,
        status TEXT NOT NULL, -- trạng thái Running|Stopped|Done
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )`
    ).run();

    // Tạo bảng campaingn_details (chi tiết của từng chiến dịch)
    this.db.prepare(
      `CREATE TABLE IF NOT EXISTS campaignconfiguration (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        campaign_id INTEGER NOT NULL,
        delay INTEGER DEFAULT 5,
        max_post INTEGER DEFAULT 1, -- tai khoan duoc post toi da bao nhieu bai
        comment_after_post BOOLEAN DEFAULT FALSE,
        comment_content TEXT,
        is_anonymous BOOLEAN DEFAULT FALSE, -- an danh
        is_joingroup BOOLEAN DEFAULT FALSE, -- tham gia nhom
        tag_friend BOOLEAN DEFAULT FALSE, -- tag ban be
        tagFollowers BOOLEAN DEFAULT FALSE, -- Tag @nguoitheodoi
        tagNeubat BOOLEAN DEFAULT FALSE, -- Tag @neubat
        FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE
      )`,
    ).run();

    // Tạo bảng danh sách campaign voi image
    this.db.prepare(
      `CREATE TABLE IF NOT EXISTS Image (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        campaign_id INTEGER NOT NULL,
        image TEXT,
        FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE
      )`
    ).run();

    // Tạo bảng danh sách nhom cần gửi bài
    this.db.prepare(
      `CREATE TABLE IF NOT EXISTS groups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        campaign_id INTEGER NOT NULL,
        groupID TEXT NOT NULL,
        postId TEXT,
        status TEXT NOT NULL, -- trang thái new|false|pending|public
        FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE
      )`
    ).run();

    // Tạo bảng các tai khoản đã gửi bài cho nhóm
    this.db.prepare(
      `CREATE TABLE IF NOT EXISTS UserGroup (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        groupId INTEGER,
        profileId TEXT,
        userpost TEXT,
        status TEXT, -- trang thái done|false
        FOREIGN KEY (groupId) REFERENCES groups(id) ON DELETE CASCADE
      )`
    ).run();

    // Tạo bảng danh sách nhom cần gửi bài
    this.db.prepare(
      `CREATE TABLE IF NOT EXISTS User (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        campaign_id INTEGER NOT NULL,
        profileId TEXT NOT NULL,
        numbersend INTEGER ,
        FOREIGN KEY (campaign_id) REFERENCES campaigns(id) ON DELETE CASCADE
      )`
    ).run();
  }
}

export default DatabaseManager;
