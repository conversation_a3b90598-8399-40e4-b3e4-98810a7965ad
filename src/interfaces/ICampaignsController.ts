import { Campaign, CampaignConfiguration, CampaignConfigurationRequest, CampaignDetails, CampaignDetailsRequest, GroupRequest, ImageRequest, UserGroup } from './Campaigns';

export interface ICampaignsController {
  createCampaign(data: CampaignDetailsRequest) : { success: boolean; data?: Campaign; error?: string };
  saveCampaignConfig(data: CampaignConfigurationRequest): { success: boolean; error?: string }
  saveCampaignGroup(data: GroupRequest): { success: boolean; error?: string }
  saveCampaignImage(data: ImageRequest): { success: boolean; error?: string }
  getAllCampaigns(type?: string) : Campaign[];
  getCampaignDetails(id: string): CampaignDetails | null;
  updateCampaign(data: CampaignDetailsRequest): { success: boolean; data?: Campaign; error?: string };
  deleteCampaign(id: number): { success: boolean };
  getCampaign(id: string): { success: boolean; data? : Campaign }
  bulkDeleteCampaigns(ids: string[]): boolean;
  getuserpost(groupID: string): UserGroup;
}
