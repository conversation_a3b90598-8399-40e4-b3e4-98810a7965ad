export interface Group {
  id?: string;
  campaign_id: string;
  groupID: string;
  postId: string;
  status: string;
  UserGroup?: UserGroup[]
}

export interface GroupRequest {
  campaignId: string;
  groupIds: string[];
  status?: string;
}
export interface CampaignConfiguration {
  id: string;
  campaign_id: string;
  delay?: number; // default 5
  max_post?: number; // default 1
  comment_after_post?: boolean; // default false
  comment_content?: string; // có thể null
  is_anonymous?: boolean; // default false
  is_joingroup?: boolean; // default false
  tag_friend?: boolean; // default false
  tagFollowers?: boolean; // Tag @nguoitheodoi
  tagNeubat?: boolean; // Tag @neubat
}

export interface CampaignConfigurationRequest {
  campaignId: string;
  delay: number;
  max_post: number;
  comment_after_post: boolean;
  comment_content: string;
  is_anonymous: boolean;
  is_joingroup: boolean;
  tag_friend: boolean;
  tagFollowers: boolean;
  tagNeubat: boolean;
  status?: string;
}

export interface Image {
  id?: string;
  image: string;
  campaign_id: string;
}

export interface ImageRequest {
  imagePaths: string[];
  campaignId: string;
}

export interface Campaign {
  id: string;
  type: string;
  name: string;
  status: string;
  message: string;
  created_at?: string;
  updated_at?: string;
}

export interface CampaignRequest {
  id?: string;
  type: string;
  name: string;
  message?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
}

export interface User {
  id?: number;
  campaign_id: number;
  profileId: string;
  numbersend?: number;
}

export interface UserGroup {
  id?: number;
  groupId: string;
  userpost: string;
  profileId: string;
  status: string;
}

export interface CampaignDetails {
  Campaign?: Campaign;
  CampaignConfiguration?: CampaignConfiguration;
  Group?: Group[];
  Image?: Image[];
  User?: User[];
}

export interface CampaignDetailsRequest {
  id?: string;
  type: string;
  name: string;
  status: string;
  message?: string;

  delay: number;
  max_post: number;
  comment_after_post: boolean;
  comment_content?: string;
  is_anonymous: boolean;
  is_joingroup: boolean;
  tag_friend: boolean;

  tagFollowers: boolean;
  tagNeubat: boolean;

  imagePaths: string[];
  groupIds?: string[];

  postIds?: string[];
}

export interface CampaignDetailsForRun {
  Campaign: Campaign;
  CampaignConfiguration: CampaignConfigurationRequest;
  groupIds: string[];
  imagePaths: string[];
  User: User[];
}

export interface CampaignProgress {
  message: string;
  campaignId: string;
  action?: string; // 'stopped' | 'running' | 'done'
}
