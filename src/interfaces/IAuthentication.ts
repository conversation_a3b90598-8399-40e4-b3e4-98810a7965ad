/**
 * Authentication interfaces and types
 * Based on the C# implementation requirements
 */

// Base response interface
export interface BaseResponse {
  success: boolean;
  message?: string;
  error?: string;
}

// Login request interface
export interface LoginRequest {
  email: string;
  password: string;
}

// Login response interface
export interface LoginResponse extends BaseResponse {
  data?: {
    user: User;
    token: string;
    sessionId: string;
    expiresAt: string;
  };
}

// Registration request interface (matching C# multipart form data fields)
export interface RegisterRequest {
  email: string;
  password: string;
  password_confirmation: string;
  phone: string;
  full_name: string;
  app_code: string;
  referal_code: string;
}

// Registration response interface
export interface RegisterResponse extends BaseResponse {
  data?: {
    user: User;
    token?: string;
    sessionId?: string;
  };
}

// User interface
export interface User {
  id: string;
  email: string;
  full_name: string;
  phone: string;
  app_code: string;
  referal_code?: string;
  created_at: string;
  updated_at: string;
  email_verified_at?: string;
  status: 'active' | 'inactive' | 'pending';
}

// Refresh token request interface
export interface RefreshTokenRequest {
  refreshToken?: string;
}

// Refresh token response interface
export interface RefreshTokenResponse extends BaseResponse {
  data?: {
    token: string;
    expiresAt: string;
  };
}

// Session sync request interface
export interface SyncSessionRequest {
  sessionId: string;
}

// Session sync response interface
export interface SyncSessionResponse extends BaseResponse {
  data?: {
    user: User;
    token: string;
    sessionId: string;
    expiresAt: string;
  };
}

// Logout response interface
export interface LogoutResponse extends BaseResponse {
  // No additional data needed for logout
}

// Authentication state interface for Redux store
export interface AuthenticationState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  sessionId: string | null;
  expiresAt: string | null;
  isLoading: boolean;
  error: string | null;
  lastLoginAt: string | null;
}

// Token storage interface
export interface TokenStorage {
  token: string;
  sessionId: string;
  expiresAt: string;
  user: User;
}

// Authentication service interface
export interface IAuthenticationService {
  login(request: LoginRequest): Promise<LoginResponse>;
  register(request: RegisterRequest): Promise<RegisterResponse>;
  logout(): Promise<LogoutResponse>;
  refreshToken(request?: RefreshTokenRequest): Promise<RefreshTokenResponse>;
  syncSession(request: SyncSessionRequest): Promise<SyncSessionResponse>;
  getCurrentUser(): Promise<User | null>;
  isTokenValid(): boolean;
  clearAuthData(): void;
}

// Authentication controller interface
export interface IAuthenticationController {
  login(request: LoginRequest): Promise<LoginResponse>;
  register(request: RegisterRequest): Promise<RegisterResponse>;
  logout(): Promise<LogoutResponse>;
  refreshToken(): Promise<RefreshTokenResponse>;
  syncSession(sessionId: string): Promise<SyncSessionResponse>;
  getCurrentUser(): Promise<User | null>;
  checkAuthStatus(): Promise<{ isAuthenticated: boolean; user: User | null }>;
}

// HTTP client configuration interface
export interface HttpClientConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
}

// HTTP response interface
export interface HttpResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

// Error response interface
export interface ErrorResponse {
  message: string;
  code?: string;
  details?: any;
  status?: number;
}

// Form validation errors interface
export interface ValidationErrors {
  email?: string;
  password?: string;
  password_confirmation?: string;
  phone?: string;
  full_name?: string;
  app_code?: string;
  referal_code?: string;
  general?: string;
}

// Authentication form state interface
export interface AuthFormState {
  isSubmitting: boolean;
  errors: ValidationErrors;
  touched: Record<string, boolean>;
}

export default {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  User,
  RefreshTokenRequest,
  RefreshTokenResponse,
  SyncSessionRequest,
  SyncSessionResponse,
  LogoutResponse,
  AuthenticationState,
  TokenStorage,
  IAuthenticationService,
  IAuthenticationController,
  HttpClientConfig,
  HttpResponse,
  ErrorResponse,
  ValidationErrors,
  AuthFormState,
};
