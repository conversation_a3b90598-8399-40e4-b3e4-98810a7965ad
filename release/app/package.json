{"name": "electron-react-boilerplate", "version": "4.6.0", "description": "A foundation for scalable desktop apps", "license": "MIT", "author": {"name": "Electron React Boilerplate Maintainers", "email": "<EMAIL>", "url": "https://github.com/electron-react-boilerplate"}, "main": "./dist/main/main.js", "scripts": {"rebuild": "node -r ts-node/register ../../.erb/scripts/electron-rebuild.js", "postinstall": "npm run rebuild && npm run link-modules", "link-modules": "node -r ts-node/register ../../.erb/scripts/link-modules.ts"}, "dependencies": {"better-sqlite3": "^12.2.0"}}