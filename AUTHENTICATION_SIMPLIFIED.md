# 🚀 Simplified Authentication System

## 📋 **Overview**
<PERSON><PERSON> chuyển đổi từ IPC-based authentication sang **Direct API approach** - gọi API trực tiếp từ React components mà không cần thông qua main process.

## ✅ **Files Đã Xóa (IPC-based components)**
- `src/ipc/authenticationHandlers.ts` - IPC handlers cho authentication
- `src/controllers/AuthenticationController.ts` - Controller x<PERSON> lý business logic
- `src/services/AuthenticationService.ts` - Service gọi API thông qua IPC
- `src/services/TokenStorageService.ts` - Token storage trong main process
- `src/renderer/store/slices/authSlice.ts` - Redux slice cho auth state
- `src/types/global.d.ts` - Type definitions cho window.authAPI
- `src/renderer/components/settings/EnvironmentSettings.tsx` - Environment settings UI
- `scripts/validate-authentication.js` - Validation script

## 🔧 **Files Đã Cậ<PERSON>t**
- `src/ipc/ipcHandlers.ts` - Xóa import authenticationHandlers
- `src/main/preload.ts` - Xóa authAPI exposure
- `src/renderer/store/reducer.ts` - Xóa auth slice
- `src/renderer/components/auth/AuthGuard.tsx` - Sử dụng DirectAuthService
- `src/renderer/components/auth/GuestGuard.tsx` - Sử dụng DirectAuthService
- `src/renderer/views/pages/authentication/auth-forms/AuthLogin.tsx` - Sử dụng useAuth hook
- `src/renderer/views/pages/authentication/auth-forms/AuthRegister.tsx` - Sử dụng useAuth hook

## 🆕 **Files Mới (Simplified approach)**
- `src/renderer/services/DirectAuthService.ts` - Direct API calls service
- `src/renderer/hooks/useAuth.ts` - Custom hook cho authentication
- `src/renderer/views/pages/authentication/auth-forms/AuthLoginSimplified.tsx` - Example simplified login

## 🏗️ **Architecture Mới**

### **Before (IPC-based):**
```
React Component → preload.ts (authAPI) → IPC → Main Process → HTTP API → Server
```

### **After (Direct API):**
```
React Component → useAuth Hook → DirectAuthService → HTTP API → Server
```

## 📁 **Core Files Structure**

### **1. DirectAuthService** (`src/renderer/services/DirectAuthService.ts`)
- Gọi API trực tiếp từ renderer process
- Xử lý multipart form data cho registration
- Lưu tokens trong localStorage
- Quản lý authentication state

### **2. useAuth Hook** (`src/renderer/hooks/useAuth.ts`)
- Custom React hook cho authentication
- Quản lý loading states và errors
- Tự động navigate sau login/logout
- Cung cấp các functions: login, register, logout, refreshToken

### **3. Route Guards**
- **AuthGuard**: Bảo vệ routes cần authentication
- **GuestGuard**: Redirect authenticated users khỏi auth pages

## 🎯 **Cách Sử Dụng**

### **1. Trong Login Component:**
```tsx
import useAuth from '../../../hooks/useAuth';

function LoginComponent() {
  const { login, isLoading, error, clearError } = useAuth();
  
  const handleLogin = async (values) => {
    await login(values);
  };
  
  // Render form...
}
```

### **2. Trong Register Component:**
```tsx
import useAuth from '../../../hooks/useAuth';

function RegisterComponent() {
  const { register, isLoading, error, clearError } = useAuth();
  
  const handleRegister = async (values) => {
    await register({
      email: values.email,
      password: values.password,
      password_confirmation: values.password_confirmation,
      phone: values.phone,
      full_name: values.full_name,
      app_code: values.app_code,
      referal_code: values.referal_code || '',
    });
  };
  
  // Render form...
}
```

### **3. Bảo vệ Routes:**
```tsx
// Protected routes
<Route path="/dashboard" element={
  <AuthGuard>
    <Dashboard />
  </AuthGuard>
} />

// Guest routes (login, register)
<Route path="/login" element={
  <GuestGuard>
    <Login />
  </GuestGuard>
} />
```

## ⚙️ **Configuration**

### **API Endpoints** (trong `src/config/ApiConfig.ts`):
- Development: `http://************:8899`
- Testing: `https://api-dev.phanmemtop.vn`
- Production: `https://api.phanmemtop.vn`

### **Token Storage**:
- Sử dụng `localStorage` thay vì Electron secure storage
- Keys: `auth_token`, `auth_session`, `auth_user`

## 🔒 **Security Considerations**

### **Pros của Simplified Approach:**
- ✅ Đơn giản hơn, ít code hơn
- ✅ Dễ debug và maintain
- ✅ Tương tự web apps thông thường
- ✅ Performance tốt hơn (không có IPC overhead)

### **Cons:**
- ⚠️ Tokens lưu trong localStorage (ít secure hơn)
- ⚠️ Renderer process có thể access API directly
- ⚠️ Không có main process validation layer

## 🚀 **Next Steps**

1. **Test authentication flow:**
   ```bash
   npm start
   ```

2. **Configure API endpoints** trong `ApiConfig.ts`

3. **Test với real API endpoints**

4. **Customize error messages** trong `ErrorHandler`

5. **Add additional features** nếu cần:
   - Remember me functionality
   - Auto token refresh
   - Offline support

## 📝 **Notes**

- Approach này phù hợp cho **API-based authentication**
- Nếu cần **high security**, có thể quay lại IPC approach
- Có thể mix cả hai approaches cho different features
- Environment switching vẫn hoạt động thông qua `ApiConfig`
